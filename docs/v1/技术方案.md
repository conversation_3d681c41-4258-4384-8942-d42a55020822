# AI推理平台技术方案

## 1. 项目背景与目标

### 1.1 背景

随着AI在各行业的普及，模型推理服务需求激增。现有方案普遍存在插件集成难、资源浪费、调度能力弱、结果复用差、文件管理低效等问题。

### 1.2 目标

- 构建插件化、可扩展的AI推理平台
- 支持模型动态加载/卸载与高效资源调度
- 实现任务优先级调度与结果缓存复用
- 提供统一高效的文件管理
- 标准化HTTP API，便于集成
- 保证系统可维护、可扩展

## 2. 系统设计

### 2.1 分层架构与整体架构图

```mermaid
flowchart TD
    subgraph API["FastAPI应用层"]
        plugin_api["插件管理API"]
        task_api["任务管理API"]
        file_api["文件管理API"]
        sys_api["系统管理API"]
        middleware["中间件/公共组件"]
    end

    subgraph Logic["核心业务逻辑层"]
        plugin_mgr["插件管理器"]
        task_scheduler["任务调度器"]
        file_service["文件服务"]
        sys_monitor["系统监控器"]
        common_service["公共服务"]
    end

    subgraph Engine["推理引擎层"]
        engine_pool["推理引擎实例池"]
        plugin_loader["插件加载器"]
        plugin_executor["插件执行器"]
        subgraph inference_factory["推理引擎工厂"]
            onnx_engine["ONNX引擎"]
            pt_engine["PyTorch引擎"]
            cv_engine["OpenCV引擎"]
            api_engine["API引擎"]
        end
    end

    subgraph Infra["基础设施层"]
        postgres[(PostgreSQL)]
        redis[(Redis)]
        oss[("阿里云OSS")]
        local_storage[("本地文件存储")]
    end

    plugin_api --> plugin_mgr
    task_api --> task_scheduler
    file_api --> file_service
    sys_api --> sys_monitor

    plugin_mgr --> engine_pool
    plugin_mgr --> plugin_loader
    task_scheduler --> plugin_executor
    file_service --> oss
    file_service --> local_storage

    plugin_executor --> engine_pool
    plugin_loader --> inference_factory

    plugin_mgr --> postgres
    task_scheduler --> postgres
    task_scheduler --> redis
    file_service --> postgres
    sys_monitor --> postgres
```

### 2.2 关键组件

- **推理引擎工厂（InferenceEngineFactory）**：统一创建ONNX、PyTorch、OpenCV、API等推理引擎
- **推理引擎实例池（EngineInstancePool）**：管理推理引擎（模型会话）的复用与生命周期
- **插件加载器/执行器**：负责插件的加载、预处理、推理、后处理
- **文件服务（file_service）**：统一管理文件的上传、下载、存储、元数据
- **任务调度器**：基于优先级调度任务，保证高优先级任务整体优先完成

### 2.3 核心技术栈

| 类别 | 技术选择 | 版本 | 说明 |
|------|----------|------|------|
| Web框架 | FastAPI | 0.95+ | 高性能异步API框架，支持OpenAPI自动文档化 |
| 数据库 | PostgreSQL | 14+ | 强大的关系型数据库，支持JSONB、全文索引等高级特性 |
| ORM | SQLAlchemy | 2.0+ | 功能完善的ORM框架，支持异步操作 |
| 缓存/队列 | Redis | 7.0+ | 高性能内存数据结构存储，用于任务队列和缓存 |
| 推理引擎 | ONNX Runtime | 1.15+ | 用于ONNX格式模型的高效推理 |
| 推理引擎 | PyTorch | 2.0+ | 用于直接加载和运行PyTorch模型 |
| 推理引擎 | OpenCV | 4.8+ | 用于图像处理和自定义视觉算法 |

### 2.4 目录结构
项目采用分层架构的模块化设计，目录结构如下：
```
/ai-inference-platform
│
├── main.py                 # 应用入口（位于根目录）
│
├── /app                    # 主应用目录
│   ├── /api                # API路由定义
│   │   ├── /v1             # API版本1
│   │   │   ├── plugin.py   # 插件管理API
│   │   │   ├── task.py     # 任务管理API
│   │   │   ├── file.py     # 文件管理API
│   │   │   └── system.py   # 系统管理API
│   │   └── deps.py         # API依赖项（如认证）
│   │
│   ├── /core               # 核心业务逻辑
│   │   ├── /plugin         # 插件管理相关
│   │   │   ├── manager.py  # 插件管理器
│   │   │   ├── validator.py# 插件验证器
│   │   │   └── loader.py   # 插件加载器
│   │   │
│   │   ├── /task           # 任务管理相关
│   │   │   ├── manager.py  # 任务管理器
│   │   │   ├── scheduler.py# 任务调度器
│   │   │   └── processor.py# 任务处理器
│   │   │
│   │   ├── /file           # 文件管理相关
│   │   │   ├── manager.py  # 文件管理器
│   │   │   └── storage.py  # 存储服务
│   │   │
│   │   └── /cache          # 缓存管理相关
│   │       └── service.py  # 结果缓存服务
│   │
│   ├── /engines            # 推理引擎实现
│   │   ├── factory.py      # 推理引擎工厂
│   │   ├── base.py         # 推理引擎基类
│   │   ├── onnx.py         # ONNX Runtime引擎
│   │   ├── pytorch.py      # PyTorch引擎
│   │   ├── opencv.py       # OpenCV引擎
│   │   └── api.py          # API引擎
│   │
│   ├── /models             # 数据库模型定义
│   │   ├── plugin.py       # 插件模型
│   │   ├── task.py         # 任务模型
│   │   ├── file.py         # 文件模型
│   │   └── cache.py        # 缓存模型
│   │
│   ├── /schemas            # 请求/响应模式定义
│   │   ├── plugin.py       # 插件相关模式
│   │   ├── task.py         # 任务相关模式
│   │   ├── file.py         # 文件相关模式
│   │   └── common.py       # 通用模式
│   │
│   ├── /db                 # 数据库相关
│   │   ├── session.py      # 数据库会话
│   │   └── base.py         # 基础模型
│   │
│   ├── /utils              # 工具函数
│   │   ├── logging.py      # 日志工具
│   │   ├── security.py     # 安全相关
│   │   ├── hasher.py       # 文件哈希计算
│   │   └── helpers.py      # 辅助函数
│   │
│   └── /config             # 配置管理
│       └── settings.py     # 应用配置
│
├── /tests                  # 测试代码
│   ├── /api                # API测试
│   ├── /core               # 核心逻辑测试
│   └── /engines            # 推理引擎测试
│
├── /plugins                # 本地插件存储目录
│   ├── /plugin_code_1      # 按插件code组织
│   │   └── /v1.0.0         # 按版本组织
│   │       ├── model.onnx  # 模型文件
│   │       ├── config.yaml # 配置文件
│   │       └── plugin.py   # Python逻辑文件
│   └── /plugin_code_2      # 其他插件
│
├── /data                   # 数据存储目录
│   └── /files              # 文件存储
│       ├── /image          # 按文件类型组织
│       ├── /video          # 按文件类型组织
│       └── /temp           # 临时文件
│
├── /docs                   # 文档
│   ├── API.md              # API文档
│   └── 技术方案.md          # 技术方案
│
├── /scripts                # 辅助脚本
│   └── init_db.py          # 数据库初始化
│
└── requirements.txt        # 依赖管理
```

## 3. 数据模型设计

### 3.1 表SQL

```sql
-- 插件表：存储插件元数据和文件路径信息
CREATE TABLE plugin (
    id BIGINT PRIMARY KEY, -- 雪花ID，插件唯一标识
    plugin_code VARCHAR(100) NOT NULL, -- 插件唯一编码
    plugin_version VARCHAR(20) NOT NULL, -- 插件版本号
    name VARCHAR(100) NOT NULL, -- 插件名称
    description TEXT, -- 插件描述
    type VARCHAR(50) NOT NULL, -- 插件类型，如quantification/detection/mosaic
    engine VARCHAR(50) NOT NULL, -- 推理引擎类型，如onnx/pytorch/opencv/api
    input_file_type JSONB NOT NULL, -- 支持的输入文件类型
    input_media_type JSONB NOT NULL, -- 支持的输入媒体类型
    classes JSONB NOT NULL, -- 支持的类别列表
    author VARCHAR(100), -- 作者
    status VARCHAR(20) NOT NULL DEFAULT 'enabled', -- 状态
    model_file_path TEXT NOT NULL, -- 模型文件路径
    config_file_path TEXT NOT NULL, -- 配置文件路径
    python_file_path TEXT NOT NULL, -- Python处理逻辑文件路径
    is_deleted BOOLEAN NOT NULL DEFAULT false, -- 逻辑删除标记
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    UNIQUE(plugin_code, plugin_version, is_deleted)
);
-- 索引
CREATE INDEX idx_plugin_status ON plugin(status);
CREATE INDEX idx_plugin_is_deleted ON plugin(is_deleted);

-- 文件表：存储文件元数据和存储路径
CREATE TABLE file (
    id BIGINT PRIMARY KEY, -- 雪花ID，文件唯一标识
    url TEXT NOT NULL, -- 文件唯一访问URL
    size BIGINT NOT NULL, -- 文件大小(字节)
    file_name VARCHAR(255) NOT NULL, -- 原始文件名
    oss_path TEXT NOT NULL, -- OSS存储路径
    local_path TEXT, -- 本地存储路径
    content_type VARCHAR(100) NOT NULL, -- MIME类型
    file_type VARCHAR(20) NOT NULL, -- 文件类型
    media_type VARCHAR(50) NOT NULL, -- 媒体类型
    file_hash VARCHAR(64) NOT NULL UNIQUE, -- SHA256哈希值
    file_metadata JSONB, -- 文件元数据
    platform VARCHAR(50), -- 文件来源平台
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 索引
CREATE UNIQUE INDEX idx_file_hash ON file(file_hash);
CREATE INDEX idx_file_url ON file(url);

-- 任务表：存储任务基本信息和状态
CREATE TABLE task (
    task_id BIGINT PRIMARY KEY, -- 雪花ID，任务唯一标识
    completed_items INTEGER NOT NULL DEFAULT 0, -- 已完成的任务项数量
    total_items INTEGER NOT NULL DEFAULT 0, -- 总任务项数量
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 任务状态
    priority INTEGER NOT NULL DEFAULT 1, -- 优先级
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    started_at TIMESTAMP WITH TIME ZONE, -- 启动时间
    completed_at TIMESTAMP WITH TIME ZONE -- 完成时间
);
-- 索引
CREATE INDEX idx_task_status ON task(status);
CREATE INDEX idx_task_priority ON task(priority);

-- 任务项表：存储任务项详细信息和处理结果
CREATE TABLE task_item (
    task_item_id BIGINT PRIMARY KEY, -- 雪花ID，任务项唯一标识
    task_id BIGINT NOT NULL, -- 关联任务ID
    data_id VARCHAR(100), -- 外部系统数据ID
    file_type VARCHAR(20) NOT NULL, -- 文件类型
    media_type VARCHAR(50) NOT NULL, -- 媒体类型
    file_id BIGINT NOT NULL, -- 关联文件ID
    file_url TEXT NOT NULL, -- 文件URL
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 任务项状态
    error TEXT, -- 错误信息
    plugin_code VARCHAR(100) NOT NULL, -- 插件编码
    plugin_version VARCHAR(20) NOT NULL, -- 插件版本
    result JSONB, -- 推理结果
    params JSONB, -- 任务项参数
    from_cache BOOLEAN NOT NULL DEFAULT false, -- 是否来自缓存
    process_time JSONB, -- 处理各阶段时间
    started_at TIMESTAMP WITH TIME ZONE, -- 启动时间
    completed_at TIMESTAMP WITH TIME ZONE -- 完成时间
);
-- 索引
CREATE INDEX idx_taskitem_task_id ON task_item(task_id);
CREATE INDEX idx_taskitem_file_id ON task_item(file_id);
CREATE INDEX idx_taskitem_plugin ON task_item(plugin_code, plugin_version);
CREATE INDEX idx_taskitem_status ON task_item(status);

-- 结果缓存表：存储推理结果缓存
CREATE TABLE result_cache (
    id BIGINT PRIMARY KEY, -- 雪花ID，缓存唯一标识
    file_hash VARCHAR(64) NOT NULL, -- 文件哈希
    plugin_code VARCHAR(100) NOT NULL, -- 插件编码
    plugin_version VARCHAR(20) NOT NULL, -- 插件版本
    result JSONB NOT NULL, -- 缓存结果
    process_time JSONB, -- 处理各阶段时间
    use_count INTEGER NOT NULL DEFAULT 0, -- 缓存使用次数
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    UNIQUE(file_hash, plugin_code, plugin_version)
);
-- 索引
CREATE UNIQUE INDEX idx_resultcache_composite ON result_cache(file_hash, plugin_code, plugin_version);

-- 字段注释
COMMENT ON TABLE plugin IS '插件表，存储插件元数据和文件路径信息';
COMMENT ON COLUMN plugin.id IS '雪花ID，插件唯一标识';
COMMENT ON COLUMN plugin.plugin_code IS '插件唯一编码';
COMMENT ON COLUMN plugin.plugin_version IS '插件版本号';
COMMENT ON COLUMN plugin.name IS '插件名称';
COMMENT ON COLUMN plugin.description IS '插件描述';
COMMENT ON COLUMN plugin.type IS '插件类型，如quantification/detection/mosaic';
COMMENT ON COLUMN plugin.engine IS '推理引擎类型，如onnx/pytorch/opencv/api';
COMMENT ON COLUMN plugin.input_file_type IS '支持的输入文件类型';
COMMENT ON COLUMN plugin.input_media_type IS '支持的输入媒体类型';
COMMENT ON COLUMN plugin.classes IS '支持的类别列表';
COMMENT ON COLUMN plugin.author IS '作者';
COMMENT ON COLUMN plugin.status IS '状态';
COMMENT ON COLUMN plugin.model_file_path IS '模型文件路径';
COMMENT ON COLUMN plugin.config_file_path IS '配置文件路径';
COMMENT ON COLUMN plugin.python_file_path IS 'Python处理逻辑文件路径';
COMMENT ON COLUMN plugin.is_deleted IS '逻辑删除标记';
COMMENT ON COLUMN plugin.created_at IS '创建时间';
COMMENT ON COLUMN plugin.updated_at IS '更新时间';

COMMENT ON TABLE file IS '文件表，存储文件元数据和存储路径';
COMMENT ON COLUMN file.id IS '雪花ID，文件唯一标识';
COMMENT ON COLUMN file.url IS '文件唯一访问URL';
COMMENT ON COLUMN file.size IS '文件大小(字节)';
COMMENT ON COLUMN file.file_name IS '原始文件名';
COMMENT ON COLUMN file.oss_path IS 'OSS存储路径';
COMMENT ON COLUMN file.local_path IS '本地存储路径';
COMMENT ON COLUMN file.content_type IS 'MIME类型';
COMMENT ON COLUMN file.file_type IS '文件类型';
COMMENT ON COLUMN file.media_type IS '媒体类型';
COMMENT ON COLUMN file.file_hash IS 'SHA256哈希值';
COMMENT ON COLUMN file.file_metadata IS '文件元数据';
COMMENT ON COLUMN file.platform IS '文件来源平台';
COMMENT ON COLUMN file.created_at IS '创建时间';
COMMENT ON COLUMN file.updated_at IS '更新时间';

COMMENT ON TABLE task IS '任务表，存储任务基本信息和状态';
COMMENT ON COLUMN task.task_id IS '雪花ID，任务唯一标识';
COMMENT ON COLUMN task.completed_items IS '已完成的任务项数量';
COMMENT ON COLUMN task.total_items IS '总任务项数量';
COMMENT ON COLUMN task.status IS '任务状态';
COMMENT ON COLUMN task.priority IS '优先级';
COMMENT ON COLUMN task.created_at IS '创建时间';
COMMENT ON COLUMN task.updated_at IS '更新时间';
COMMENT ON COLUMN task.started_at IS '启动时间';
COMMENT ON COLUMN task.completed_at IS '完成时间';

COMMENT ON TABLE task_item IS '任务项表，存储任务项详细信息和处理结果';
COMMENT ON COLUMN task_item.task_item_id IS '雪花ID，任务项唯一标识';
COMMENT ON COLUMN task_item.task_id IS '关联任务ID';
COMMENT ON COLUMN task_item.data_id IS '外部系统数据ID';
COMMENT ON COLUMN task_item.file_type IS '文件类型';
COMMENT ON COLUMN task_item.media_type IS '媒体类型';
COMMENT ON COLUMN task_item.file_id IS '关联文件ID';
COMMENT ON COLUMN task_item.file_url IS '文件URL';
COMMENT ON COLUMN task_item.status IS '任务项状态';
COMMENT ON COLUMN task_item.error IS '错误信息';
COMMENT ON COLUMN task_item.plugin_code IS '插件编码';
COMMENT ON COLUMN task_item.plugin_version IS '插件版本';
COMMENT ON COLUMN task_item.result IS '推理结果';
COMMENT ON COLUMN task_item.params IS '任务项参数';
COMMENT ON COLUMN task_item.from_cache IS '是否来自缓存';
COMMENT ON COLUMN task_item.process_time IS '处理各阶段时间';
COMMENT ON COLUMN task_item.started_at IS '启动时间';
COMMENT ON COLUMN task_item.completed_at IS '完成时间';

COMMENT ON TABLE result_cache IS '结果缓存表，存储推理结果缓存';
COMMENT ON COLUMN result_cache.id IS '雪花ID，缓存唯一标识';
COMMENT ON COLUMN result_cache.file_hash IS '文件哈希';
COMMENT ON COLUMN result_cache.plugin_code IS '插件编码';
COMMENT ON COLUMN result_cache.plugin_version IS '插件版本';
COMMENT ON COLUMN result_cache.result IS '缓存结果';
COMMENT ON COLUMN result_cache.process_time IS '处理各阶段时间';
COMMENT ON COLUMN result_cache.use_count IS '缓存使用次数';
COMMENT ON COLUMN result_cache.created_at IS '创建时间';
COMMENT ON COLUMN result_cache.updated_at IS '更新时间';
```

### 3.2 任务与任务项状态流转

```
PENDING（等待执行）
   ↓
RUNNING（正在执行）
   ↓
COMPLETED（执行完成）
   ↘
    FAILED（执行失败）
   ↘
    CANCELED（已取消）
```
- **PENDING**：已创建，等待调度
- **RUNNING**：正在处理
- **COMPLETED**：成功完成
- **FAILED**：处理失败
- **CANCELED**：被取消

## 4. 文件管理与存储

### 4.1 存储类型
- **临时存储**：短期缓存，保留2天，适合调度前预下载
- **永久存储**：长期保存，路径格式为  `<基础路径>/<文件类型>/<媒体类型>/<文件hash>.<后缀>`

### 4.2 文件下载与存储流程
1. 任务项需要文件时，通过file_service统一发起请求
2. file_service查file表：
   - 存在且local_path不为空：直接返回本地路径
   - 存在但local_path为空：下载到永久存储，更新local_path，补全file_hash和file_metadata
   - 不存在：走文件注册/上传流程
3. 临时存储用于短期缓存，定期清理

#### 路径示例  
`/data/files/image/visible_light/abcdef123456.jpg`

## 6. 关键流程设计

### 6.1 插件注册与管理

```mermaid
sequenceDiagram
    actor client as 客户端
    participant api as 插件管理API
    participant validator as 插件验证器
    participant file_mgr as 文件管理器
    participant storage as 本地存储服务
    participant manager as 插件管理器
    participant db as PostgreSQL

    client->>api: 上传插件文件(model, config, python)
    api->>validator: 验证文件有效性
    validator->>validator: 解析配置文件
    validator->>validator: 检查Python接口实现

    alt 验证通过
        validator->>file_mgr: 计算文件哈希
        file_mgr->>storage: 存储文件到本地存储
        note right of storage: 存储路径: 插件基础目录/插件code/插件版本/
        storage-->>file_mgr: 返回存储路径
        file_mgr->>manager: 提供文件信息
        manager->>db: 检查插件是否存在(code, version, is_deleted)
        alt 插件不存在
            manager->>db: 创建新的插件记录
            db-->>manager: 插件ID
            manager-->>api: 插件创建成功
            api-->>client: 返回插件ID和信息
        else 插件已存在
            manager-->>api: 插件已存在错误
            api-->>client: 返回错误信息
        end
    else 验证失败
        validator-->>api: 返回验证错误
        api-->>client: 返回错误信息
    end
```

插件本地存储，采用以下规范：

1. **基础目录**：所有插件存储在 `PLUGIN_BASE_PATH` 配置的目录下
2. **目录结构**：`{PLUGIN_BASE_PATH}/{plugin_code}/{plugin_version}/`
3. **插件文件**：每个版本目录包含三个关键文件：
   - `model.{ext}` - 模型文件（如 .onnx, .pt 等，根据引擎类型）
   - `config.yaml` - 配置文件
   - `plugin.py` - Python处理逻辑

例如，一个名为"face_detection"的插件，版本为"1.0.0"，其存储路径为：
```
/plugins/face_detection/1.0.0/
  ├── model.onnx
  ├── config.yaml
  └── plugin.py
```

### 5.2 任务提交与执行（标准状态流转）

1. **任务提交**
   - 客户端提交任务（含多个数据项）
   - 任务管理API创建任务及任务项，所有任务项初始状态为PENDING
   - 检查结果缓存，命中则直接写入结果并将任务项状态置为COMPLETED
   - 未命中缓存，立即发起文件下载
     - 文件下载成功，不改变状态（仍为PENDING，等待调度）
     - 文件下载失败，任务项状态置为FAILED

2. **调度与执行**
   - 调度器只处理当前最高优先级任务，且必须将该任务所有PENDING任务项全部调度完毕，才切换下一个优先级
   - 任务项被调度时，状态置为RUNNING
   - 执行插件推理（预处理→推理→后处理）
     - 成功则任务项状态置为COMPLETED
     - 失败则任务项状态置为FAILED
   - 任务项全部为COMPLETED/FAILED/CANCELED后，任务状态置为COMPLETED/FAILED/CANCELED（根据业务规则）

```mermaid
sequenceDiagram
    actor client as 客户端
    participant api as 任务管理API
    participant task_mgr as 任务管理器
    participant cache as 结果缓存服务
    participant file_svc as 文件服务
    participant scheduler as 任务调度器
    participant processor as 任务处理器
    participant executor as 插件执行器
    participant engine_pool as 推理引擎实例池
    participant db as PostgreSQL
    participant redis as Redis

    client->>api: 提交任务请求(多个数据项)
    api->>task_mgr: 创建任务
    task_mgr->>db: 创建任务记录
    db-->>task_mgr: 任务ID

    loop 每个任务项
        task_mgr->>db: 创建任务项记录 (PENDING)
        db-->>task_mgr: 任务项ID
        task_mgr->>cache: 检查缓存(文件哈希+插件code+version)
        alt 缓存命中
            cache->>db: 更新缓存使用计数
            cache->>db: 更新任务项状态为COMPLETED
            cache-->>task_mgr: 返回缓存结果
        else 缓存未命中
            task_mgr->>file_svc: 请求文件下载
            alt 下载成功
                file_svc-->>task_mgr: 返回本地文件路径
                %% 状态仍为PENDING，等待调度
            else 下载失败
                file_svc-->>task_mgr: 下载失败
                task_mgr->>db: 更新任务项状态为FAILED
            end
        end
    end

    task_mgr-->>api: 返回任务ID和初始状态
    api-->>client: 返回任务信息

    %% 异步调度
    Note over scheduler,processor: 异步调度

    loop 当前最高优先级任务
        scheduler->>db: 查询最高优先级且未完成的任务
        db-->>scheduler: 任务及任务项列表
        loop 任务项
            alt 任务项状态为PENDING且文件已就绪
                scheduler->>db: 更新任务项状态为RUNNING
                scheduler->>processor: 调度任务项
                processor->>executor: 执行插件推理
                executor->>engine_pool: 获取推理引擎实例
                engine_pool-->>executor: 推理引擎实例
                executor->>executor: 预处理-推理-后处理
                alt 推理成功
                    executor-->>processor: 推理结果
                    processor->>cache: 缓存结果
                    processor->>db: 更新任务项状态为COMPLETED
                else 推理失败
                    executor-->>processor: 错误
                    processor->>db: 更新任务项状态为FAILED
                end
            else 任务项状态非PENDING
                Note right of scheduler: 跳过
            end
        end
        alt 该任务所有项已COMPLETED/FAILED/CANCELED
            scheduler->>db: 更新任务状态为COMPLETED/FAILED/CANCELED
        end
    end
```

## 6. 推理引擎工厂与实例池

### 6.1 设计目标与作用

- **推理引擎工厂（InferenceEngineFactory）**
  负责根据插件元数据和配置，动态创建不同类型的推理引擎实例（如ONNX、PyTorch、OpenCV、API等），实现推理引擎的标准化和解耦。

- **推理引擎实例池（EngineInstancePool）**
  负责推理引擎实例（模型会话）的生命周期管理，包括实例的创建、复用、并发安全、闲置回收等，提升资源利用率和系统吞吐。

### 6.2 主要类图

```mermaid
classDiagram
    class InferenceEngineFactory {
        +create_engine(engine_type, plugin_metadata) InferenceEngine
    }
    class InferenceEngine {
        <<interface>>
        +initialize(model_path, config)
        +predict(input_data)
        +release()
    }
    class ONNXRuntimeEngine
    class PyTorchEngine
    class OpenCVEngine
    class APIEngine

    class EngineInstancePool {
        +get_instance(plugin_code, plugin_version) InferenceEngine
        +release_instance(plugin_code, plugin_version)
        +cleanup_idle_instances()
    }

    InferenceEngineFactory ..> InferenceEngine : creates
    ONNXRuntimeEngine ..|> InferenceEngine
    PyTorchEngine ..|> InferenceEngine
    OpenCVEngine ..|> InferenceEngine
    APIEngine ..|> InferenceEngine
    EngineInstancePool o-- InferenceEngine
```



### 6.3 关键方法说明（伪代码）

```python
class InferenceEngineFactory:
    @staticmethod
    async def create_engine(engine_type: str, plugin_metadata: dict) -> InferenceEngine:
        """
        根据engine_type和插件元数据创建推理引擎实例
        """
        if engine_type == 'onnx':
            engine = ONNXRuntimeEngine()
        elif engine_type == 'pytorch':
            engine = PyTorchEngine()
        elif engine_type == 'opencv':
            engine = OpenCVEngine()
        elif engine_type == 'api':
            engine = APIEngine()
        else:
            raise ValueError(f"Unsupported engine type: {engine_type}")
        await engine.initialize(plugin_metadata['model_file_path'], plugin_metadata.get('engine_config', {}))
        return engine

class EngineInstancePool:
    def __init__(self, engine_factory, config):
        self.engine_factory = engine_factory
        self.instances = {}  # {key: {instance, last_used, status}}
        self.locks = {}

    async def get_instance(self, plugin_code, plugin_version):
        key = f'{plugin_code}:{plugin_version}'
        # 并发安全获取
        if key in self.instances and self.instances[key]['status'] == 'ACTIVE':
            self.instances[key]['last_used'] = now()
            return self.instances[key]['instance']
        # 加锁，防止重复加载
        async with self.locks.setdefault(key, AsyncLock()):
            if key not in self.instances or self.instances[key]['status'] != 'ACTIVE':
                # 查询插件元数据
                plugin_metadata = ... # 从数据库或缓存获取
                engine_type = plugin_metadata['engine']
                engine = await self.engine_factory.create_engine(engine_type, plugin_metadata)
                self.instances[key] = {'instance': engine, 'last_used': now(), 'status': 'ACTIVE'}
            return self.instances[key]['instance']

    async def release_instance(self, plugin_code, plugin_version):
        key = f'{plugin_code}:{plugin_version}'
        if key in self.instances:
            self.instances[key]['status'] = 'IDLE'
            self.instances[key]['last_used'] = now()

    async def cleanup_idle_instances(self, idle_timeout=300):
        # 定期清理闲置超时的实例
        for key, info in list(self.instances.items()):
            if info['status'] == 'IDLE' and (now() - info['last_used']).total_seconds() > idle_timeout:
                await info['instance'].release()
                del self.instances[key]
```

### 6.4 典型交互流程（时序图）

```mermaid
sequenceDiagram
    participant executor as 插件执行器
    participant pool as 推理引擎实例池
    participant factory as 推理引擎工厂
    participant engine as 推理引擎
    participant db as 数据库

    executor->>pool: get_instance(plugin_code, plugin_version)
    alt 实例已存在且活跃
        pool-->>executor: 返回已存在实例
    else
        pool->>db: 查询插件元数据
        db-->>pool: 返回插件元数据
        pool->>factory: create_engine(engine_type, plugin_metadata)
        factory->>engine: initialize(model_path, config)
        engine-->>factory: 初始化完成
        factory-->>pool: 返回推理引擎实例
        pool-->>executor: 返回新实例
    end
    executor->>engine: predict(input_data)
    engine-->>executor: 推理结果
    executor->>pool: release_instance(plugin_code, plugin_version)
    pool-->>executor: 确认释放
```

### 6.5 设计要点总结

- **实例唯一性**：同一插件（code+version）同一时刻只加载一个推理引擎实例，避免重复占用内存。
- **并发安全**：通过锁机制防止并发加载同一实例。
- **按需加载与自动回收**：仅在有请求时加载，闲置超时自动卸载，节省资源。
- **插件与引擎解耦**：插件只关心业务逻辑，推理引擎工厂和实例池负责模型资源管理。
- **支持多种推理后端**：ONNX、PyTorch、OpenCV、API等均可通过工厂模式扩展。

## 7. 性能与并发优化

- **推理引擎实例池**：按需加载、闲置自动卸载、实例复用
- **文件本地缓存**：高频文件本地缓存，LRU清理
- **数据库索引与批量操作**：优化查询与写入
- **异步API与后台任务**：提升并发能力
- **分布式锁与并发限制**：保证资源安全与高效

## 8. 安全与扩展性

- **认证与授权**：JWT、API Key、RBAC
- **数据安全**：HTTPS、TLS、敏感数据加密
- **插件沙箱与资源限制**：防止恶意插件滥用
- **多节点部署与负载均衡**：支持K8s、Docker Compose
- **监控与日志**：Prometheus、ELK、分布式追踪


