"""
任务管理API
"""

from fastapi import APIRouter, Depends, Query, Path, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.core.task.manager import task_manager
from app.schemas.task import (
    TaskCreate, TaskFilter, TaskItemResponse, TaskResponse
)
from app.schemas.base import BaseResponse, PageResponse, PageParams

router = APIRouter()


@router.post("", response_model=BaseResponse[TaskResponse], summary="提交任务")
async def create_task(
    task_in: TaskCreate,
    db: AsyncSession = Depends(deps.get_db)
) -> BaseResponse[TaskResponse]:
    """
    提交任务
    
    Args:
        task_in: 任务创建参数
        db: 数据库会话
        
    Returns:
        BaseResponse[TaskResponse]: 创建的任务信息
    """
    task_response = await task_manager.create_task(db, task_in=task_in)

    return BaseResponse(
        message="任务提交成功",
        data=task_response
    )


@router.get("", response_model=BaseResponse[PageResponse[TaskResponse]], summary="获取任务列表")
async def get_tasks(
    filter: TaskFilter = Depends(),
    page_params: PageParams = Depends(),
    db: AsyncSession = Depends(deps.get_db)
) -> BaseResponse[PageResponse[TaskResponse]]:
    """
    获取任务列表
    
    Args:
        filter: 过滤参数
        page_params: 分页参数
        db: 数据库会话
        
    Returns:
        任务列表分页数据
    """
    # 获取分页数据
    result = await task_manager.get_tasks(
        db,
        filter_params=filter,
        page_params=page_params)

    return BaseResponse[PageResponse[TaskResponse]](
        message="获取任务列表成功",
        data=result
    )


@router.get("/{task_id}", response_model=BaseResponse[TaskResponse], summary="获取任务详情")
async def get_task(
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(deps.get_db)
) -> BaseResponse[TaskResponse]:
    """
    获取任务详情
    
    Args:
        task_id: 任务ID
        db: 数据库会话
        
    Returns:
        BaseResponse[TaskResponse]: 任务详情
    """
    task_response = await task_manager.get_task(db, task_id=task_id)

    return BaseResponse(
        message="获取任务详情成功",
        data=task_response
    )


@router.get("/{task_id}/cancel", response_model=BaseResponse[TaskResponse], summary="取消任务")
async def cancel_task(
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(deps.get_db)
) -> BaseResponse[TaskResponse]:
    """
    取消任务

    Args:
        task_id: 任务ID
        db: 数据库会话

    Returns:
        任务信息
    """
    try:
        task_response = await task_manager.cancel_task(db, task_id=task_id)

        return BaseResponse(
            message="任务取消成功",
            data=task_response
        )
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )


@router.put("/{task_id}/priority", response_model=BaseResponse[TaskResponse], summary="调整任务优先级")
async def update_task_priority(
    task_id: int = Path(..., description="任务ID"),
    priority: int = Query(..., ge=1, le=10, description="新的优先级"),
    db: AsyncSession = Depends(deps.get_db)
) -> BaseResponse[TaskResponse]:
    """
    调整任务优先级
    
    Args:
        task_id: 任务ID
        priority: 新的优先级(1-10)
        db: 数据库会话
        
    Returns:
        BaseResponse[TaskResponse]: 更新后的任务信息
    """
    task_response = await task_manager.update_task_priority(
        db,
        task_id=task_id,
        priority=priority
    )

    return BaseResponse(
        message="任务优先级调整成功",
        data=task_response
    )


@router.get("/{task_id}/items", response_model=BaseResponse[PageResponse[TaskItemResponse]], summary="获取任务的任务项")
async def get_task_items(
    task_id: int = Path(..., description="任务ID"),
    page_params: PageParams = Depends(),
    db: AsyncSession = Depends(deps.get_db)
) -> BaseResponse[PageResponse[TaskItemResponse]]:
    """
    获取任务的任务项列表
    
    Args:
        task_id: 任务ID
        page_params: 分页参数
        db: 数据库会话
        
    Returns:
        任务项列表分页数据
    """

    # 获取分页数据
    result = await task_manager.get_task_items(
        db,
        task_id=task_id,
        page_params=page_params
    )
    
    return BaseResponse[PageResponse[TaskItemResponse]](
        message="获取任务项列表成功",
        data=result
    )