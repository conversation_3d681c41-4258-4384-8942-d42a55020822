"""
OpenAPI 文档工具函数
用于自定义 OpenAPI 文档生成，将蛇形命名转换为小驼峰命名
"""
import re
from typing import Dict, Any

from fastapi import FastAPI

from app.utils.naming import snake_to_camel
from app.log import app_logger as logger

def _process_multipart_form_data(media_content_schema: Dict[str, Any], openapi_doc: Dict[str, Any]) -> None:
    """
    处理 multipart/form-data 类型的表单字段，尝试动态获取别名或将字段名从蛇形转为小驼峰
    
    Args:
        media_content_schema: requestBody 中特定媒体类型（如 multipart/form-data）的 schema 定义部分。
        openapi_doc: 完整的 OpenAPI 文档对象，用于解析 $ref。
    """
    path_to_schema_object = media_content_schema.get("schema")
    actual_schema_definition = None

    if not path_to_schema_object:
        logger.warning(f"[_process_multipart_form_data] 'schema' key NOT FOUND in media_content_schema. Available keys: {list(media_content_schema.keys())}")
        return

    if "$ref" in path_to_schema_object:
        ref_string = path_to_schema_object["$ref"]
        parts = ref_string.split('/')
        if len(parts) == 4 and parts[0] == '#' and parts[1] == 'components' and parts[2] == 'schemas':
            schema_name = parts[3]
            component_schemas = openapi_doc.get("components", {}).get("schemas", {})
            actual_schema_definition = component_schemas.get(schema_name)
            if not actual_schema_definition:
                logger.warning(f"[_process_multipart_form_data] Could not resolve $ref: {ref_string}. Schema '{schema_name}' not found in components/schemas.")
                return
        else:
            logger.warning(f"[_process_multipart_form_data] Unsupported $ref format: {ref_string}")
            return
    else:
        # If not a $ref, assume the schema is inline (less common for request bodies in FastAPI)
        actual_schema_definition = path_to_schema_object

    if not actual_schema_definition:
        logger.warning("[_process_multipart_form_data] Schema definition could not be determined or resolved.")
        return

    if "properties" not in actual_schema_definition:
        logger.warning(f"[_process_multipart_form_data] 'properties' key NOT FOUND in resolved/actual schema definition. Available schema keys: {list(actual_schema_definition.keys())}")
        return
        
    properties = actual_schema_definition.get("properties", {}) # Use .get for safety, though checked above
    logger.info(f"[_process_multipart_form_data] Processing schema for multipart/form-data. Initial properties count: {len(properties)}. Transformed properties will be logged if changes occur.")
    
    initial_keys = list(properties.keys())
    new_properties = {}
    # 用于映射原始字段名到新字段名，以便更新 required 列表
    field_name_map = {} 
    
    for prop_name, prop_info in properties.items():
        candidate_name = prop_name
        dynamic_alias = prop_info.get("x-alias")

        if dynamic_alias and isinstance(dynamic_alias, str):
            candidate_name = dynamic_alias

        if "_" in candidate_name:
            final_name = snake_to_camel(candidate_name)
        else:
            final_name = candidate_name

        new_properties[final_name] = prop_info
        if prop_name != final_name:
            field_name_map[prop_name] = final_name
            
        # 更新描述中的字段名（如果存在且原始名称在描述中）
        if "description" in prop_info and prop_name in prop_info["description"]:
            prop_info["description"] = prop_info["description"].replace(prop_name, final_name)
            
    actual_schema_definition["properties"] = new_properties
    final_keys = list(actual_schema_definition['properties'].keys())
    if initial_keys != final_keys: # Log only if there was a change
        logger.info(f"[_process_multipart_form_data] Transformed properties: {initial_keys} -> {final_keys}")
    
    # 处理 resolved_schema 中的 required 字段列表
    if "required" in actual_schema_definition and isinstance(actual_schema_definition["required"], list):
        original_required_list = list(actual_schema_definition["required"]) # Copy for logging
        new_required = []
        for required_field_name in actual_schema_definition["required"]:
            # 如果字段名在我们的映射中，使用映射后的新名称
            if required_field_name in field_name_map:
                new_required.append(field_name_map[required_field_name])
            # 如果不在映射中，但包含下划线（意味着它可能未被 x-alias 处理且需要转换）
            elif "_" in required_field_name:
                 new_required.append(snake_to_camel(required_field_name))
            # 否则，保持原样 (例如，它已经是正确的别名，或者不包含下划线)
            else:
                new_required.append(required_field_name)
        actual_schema_definition["required"] = new_required
        if original_required_list != new_required: # Log only if there was a change
            logger.info(f"[_process_multipart_form_data] Original 'required' list: {original_required_list}, Updated to: {new_required}")
    elif "required" in actual_schema_definition:
        logger.warning(f"[_process_multipart_form_data] 'required' field exists in schema but is not a list. Type: {type(actual_schema_definition['required'])}")


def _transform_openapi_paths_and_operations(openapi_schema: Dict[str, Any]) -> None:
    """
    转换 OpenAPI schema 中的路径、路径参数、操作参数和请求体中的 multipart/form-data 字段。
    将蛇形命名转换为小驼峰命名。
    """
    paths = openapi_schema.get("paths", {})
    new_paths = {}  # 创建一个新的字典来存储转换后的路径，以安全地修改键
    logger.info(f"[_transform_openapi_paths_and_operations] Starting schema transformation for {len(paths)} paths.")

    for path_url, path_item_definition in paths.items():
        transformed_path_url = path_url
        # 查找 {param_name} 格式的参数
        path_params_in_url = re.findall(r'{([a-z][a-z0-9_]*)}', path_url)
        
        for param_name_in_url in path_params_in_url:
            if "_" in param_name_in_url:  # 只转换包含下划线的参数
                camel_case_param_name = snake_to_camel(param_name_in_url)
                
                original_segment = f"{{{param_name_in_url}}}"
                new_segment = f"{{{camel_case_param_name}}}"
                
                temp_transformed_path_url = transformed_path_url.replace(original_segment, new_segment)
                
                if transformed_path_url != temp_transformed_path_url:
                    transformed_path_url = temp_transformed_path_url 
                elif original_segment in transformed_path_url: 
                    logger.warning(f"[_transform_openapi_paths_and_operations] Path URL segment replace had no effect (or segment already transformed): original_segment='{original_segment}', new_segment='{new_segment}', current_path='{transformed_path_url}'")
        
        if path_url != transformed_path_url:
            logger.info(f"[_transform_openapi_paths_and_operations] Path URL transformed: '{path_url}' -> '{transformed_path_url}'")
        
        # 2. 遍历路径下的每个 HTTP 方法 (操作)
        for method, operation_definition in path_item_definition.items():
            # 跳过非字典项，如 'summary', 'description' 可能存在于 path_item_definition 顶层
            if not isinstance(operation_definition, dict):
                continue

            # 确保处理的是有效的 HTTP 方法对应的操作
            if method.lower() not in ["get", "post", "put", "delete", "patch", "options", "head"]:
                continue
                
            # 2.1 转换操作级别参数 (通常是路径参数或查询参数的定义)
            operation_parameters = operation_definition.get("parameters", [])
            for param_details in operation_parameters:
                # 检查是否是路径参数且名称包含下划线
                if param_details.get("in") == "path":
                    param_name = param_details.get("name")
                    if param_name and "_" in param_name:
                        original_param_name_in_def = param_name
                        camel_case_param_name_in_def = snake_to_camel(param_name)
                        param_details["name"] = camel_case_param_name_in_def
                        logger.info(f"[_transform_openapi_paths_and_operations] Path parameter NAME '{original_param_name_in_def}' -> '{camel_case_param_name_in_def}' in op definition for path '{transformed_path_url}'")
                        
                        # 更新schema中的名称 (如果 schema 不是引用)
                        if "schema" in param_details and "$ref" not in param_details["schema"]:
                            # 确保 schema title 也被更新
                            param_details["schema"]["title"] = camel_case_param_name_in_def
            
            # 2.2 处理请求体 (特别是 multipart/form-data)
            if "requestBody" in operation_definition and "content" in operation_definition["requestBody"]:
                request_body_content = operation_definition["requestBody"]["content"]
                
                for media_type, media_type_schema_info in request_body_content.items():
                    if media_type.startswith("multipart/form-data"):
                        logger.info(f"[_transform_openapi_paths_and_operations] Processing multipart/form-data for path '{transformed_path_url}' (method '{method}').")
                        _process_multipart_form_data(media_type_schema_info, openapi_schema) # Pass the full openapi_schema
                        # 假设每个 content 块中只有一个 multipart/form-data 定义
                        break 
        
        new_paths[transformed_path_url] = path_item_definition # 使用转换后的 URL 作为键，值为（可能已修改的）path_item_definition

    openapi_schema["paths"] = new_paths


def customize_openapi_schema(app: FastAPI) -> None:
    """
    自定义 OpenAPI 文档生成
    将 FastAPI 生成的 OpenAPI 文档中的蛇形命名转换为小驼峰命名
    
    Args:
        app: FastAPI 应用实例
    """
    # 保存原始的 openapi 方法
    original_openapi = app.openapi
    
    def custom_openapi() -> Dict[str, Any]:
        """自定义 OpenAPI 文档生成"""
        # 检查缓存，使用getattr以防止属性不存在
        cache = getattr(app, "openapi_schema", None)
        if cache:
            return cache
            
        logger.info("[custom_openapi] Generating new OpenAPI schema.")
        # 获取原始 OpenAPI 文档
        openapi_schema = original_openapi()
        
        # 执行所有转换
        _transform_openapi_paths_and_operations(openapi_schema)
        
        # 缓存结果
        setattr(app, "openapi_schema", openapi_schema)
        return openapi_schema
    
    # 替换 openapi 方法
    app.openapi = custom_openapi 