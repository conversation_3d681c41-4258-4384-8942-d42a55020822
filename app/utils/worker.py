"""
Worker管理工具
负责为整个worker进程生成和管理统一的Worker ID
"""
import os
import time
from typing import Optional, Any, Coroutine
from app.core.redis import redis_manager
from app.log import logger

# Redis键，用于存储worker_id计数器（包含启动时间戳，确保不同启动实例隔离）
WORKER_ID_KEY_PREFIX = "ai_platform:worker:id_counter"
WORKER_STARTUP_KEY = "ai_platform:worker:startup_timestamp"

class WorkerManager:
    """Worker管理器 - 为整个worker进程提供统一的ID管理"""

    def __init__(self):
        self._worker_id: Optional[int] = None
        self._startup_timestamp: Optional[int] = None
        self._worker_id_key: Optional[str] = None

    async def get_worker_id(self) -> int:
        """
        获取当前worker进程的数字ID（用于雪花算法）

        范围: 0-1023 (循环使用，适配雪花算法)

        Returns:
            int: 数字Worker ID
        """
        if self._worker_id is None:
            await self._initialize_worker_id()
        assert self._worker_id is not None, "数字Worker ID初始化失败"
        return self._worker_id

    async def get_worker_name(self) -> str:
        """
        获取当前worker进程的name

        Returns:
            int: 字符串版本的Worker Name
        """
        return f"worker{self._worker_id}({os.getpid()})"

    async def _get_startup_timestamp(self) -> int:
        """获取或创建启动时间戳"""
        if self._startup_timestamp is None:
            # 尝试从Redis获取现有的启动时间戳
            existing_timestamp = await redis_manager.get(WORKER_STARTUP_KEY)

            if existing_timestamp:
                self._startup_timestamp = int(existing_timestamp)
                logger.debug(f"使用现有启动时间戳: {self._startup_timestamp}")
            else:
                # 创建新的启动时间戳
                self._startup_timestamp = int(time.time())
                await redis_manager.set(WORKER_STARTUP_KEY, str(self._startup_timestamp), expire=3600)  # 1小时过期
                logger.info(f"创建新的启动时间戳: {self._startup_timestamp}")

                # 清理旧的worker ID计数器
                await self._cleanup_old_worker_counters()

        return self._startup_timestamp

    async def _cleanup_old_worker_counters(self):
        """清理旧的worker ID计数器"""
        try:
            client = await redis_manager.client

            # 删除所有旧的worker计数器键（包括带时间戳的和不带时间戳的）
            old_keys = await client.keys(f"{WORKER_ID_KEY_PREFIX}:*")
            old_keys.extend(await client.keys(WORKER_ID_KEY_PREFIX))  # 清理旧的不带时间戳的键

            if old_keys:
                await client.delete(*old_keys)
                logger.info(f"清理了 {len(old_keys)} 个旧的worker计数器")
            else:
                logger.debug("没有发现旧的worker计数器需要清理")

        except Exception as e:
            logger.warning(f"清理旧worker计数器失败: {e}")

    async def _get_worker_id_key(self) -> str:
        """获取当前启动实例的worker ID键"""
        if self._worker_id_key is None:
            timestamp = await self._get_startup_timestamp()
            self._worker_id_key = f"{WORKER_ID_KEY_PREFIX}:{timestamp}"
        return self._worker_id_key

    async def _initialize_worker_id(self):
        """初始化Worker ID"""
        try:
            # 获取当前启动实例的worker ID键
            worker_id_key = await self._get_worker_id_key()

            # 使用INCR原子操作，确保每个worker获取到唯一的ID
            worker_count = await redis_manager.incr(worker_id_key)

            # 生成数字格式的Worker ID（用于雪花算法）
            # 使用worker_count模1024，确保在0-1023范围内循环（雪花算法支持的最大范围）
            self._worker_id = (worker_count - 1) % 1024

            logger.info(f"Worker ID初始化成功 (数字ID: {self._worker_id}, 计数器: {worker_count})")

        except Exception as e:
            logger.error(f"从Redis初始化Worker ID失败: {e}", exc_info=True)
            # 备用方案：使用PID模1024
            pid = os.getpid()
            self._worker_id = pid % 1024
            logger.warning(f"使用备用方案初始化Worker ID (数字ID: {self._worker_id}, PID: {pid})")

    async def cleanup_on_startup(self):
        """
        应用启动时的清理方法
        清理旧的worker相关数据，为新的启动实例做准备
        """
        try:
            logger.info("开始清理旧的worker数据...")

            # 强制重新获取启动时间戳，这会触发清理逻辑
            self._startup_timestamp = None
            self._worker_id_key = None
            await self._get_startup_timestamp()

            logger.info("Worker数据清理完成")

        except Exception as e:
            logger.error(f"Worker数据清理失败: {e}", exc_info=True)


# 创建全局Worker管理器实例
worker_manager = WorkerManager()


async def cleanup_worker_data_on_startup():
    """
    应用启动时清理worker数据的便捷函数
    建议在应用启动时调用此函数
    """
    await worker_manager.cleanup_on_startup()
