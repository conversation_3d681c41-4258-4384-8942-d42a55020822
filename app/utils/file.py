"""
文件工具模块
提供文件哈希计算、MIME类型检测、文件类型判断、文件扩展名推断和存储路径构建等功能
"""
import hashlib
import mimetypes
import os
from typing import Optional, BinaryIO, Union, Dict, Any
from fractions import Fraction
from urllib.parse import urlparse, unquote

from fastapi import UploadFile
from app.schemas.file import FileType, MediaType
from app.log import file_logger as logger
from app.config.settings import settings

# 初始化MIME类型映射
mimetypes.init()

# 尝试导入python-magic
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False
    logger.warning("未安装python-magic库，将使用mimetypes库判断MIME类型")
    
    # 尝试导入python-magic-bin (Windows平台)
    try:
        import magic
        HAS_MAGIC = True
    except ImportError:
        pass

# 尝试导入元数据提取工具
try:
    import pyexiv2
    from pyexiv2 import Image as PyexivImage
    HAS_PYEXIV2 = True
except ImportError:
    HAS_PYEXIV2 = False
    logger.warning("未安装pyexiv2: pip install pyexiv2")

try:
    import exifread
    HAS_EXIFREAD = True
except ImportError:
    HAS_EXIFREAD = False
    logger.warning("未安装exifread: pip install exifread")

# 尝试导入PIL库（图像处理）
try:
    from PIL import Image as PILImage, UnidentifiedImageError
    HAS_PIL = True
except ImportError:
    HAS_PIL = False
    logger.warning("未安装PIL库: pip install Pillow")


def calculate_file_hash(file_obj: Union[BinaryIO, bytes, str]) -> str:
    """
    计算文件的SHA256哈希值
    
    Args:
        file_obj: 文件对象、二进制内容或文件路径
        
    Returns:
        str: 文件的SHA256哈希值
    """
    sha256_hash = hashlib.sha256()
    
    if isinstance(file_obj, str):
        # 文件路径
        with open(file_obj, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
    elif isinstance(file_obj, bytes):
        # 二进制内容
        sha256_hash.update(file_obj)
    else:
        # 文件对象
        current_position = file_obj.tell()
        file_obj.seek(0)
        
        for byte_block in iter(lambda: file_obj.read(4096), b""):
            sha256_hash.update(byte_block)
            
        # 恢复文件指针位置
        file_obj.seek(current_position)
    
    return sha256_hash.hexdigest()


def get_mime_type(file_path_or_content: Union[str, bytes, BinaryIO, UploadFile]) -> str:
    """
    获取文件的MIME类型
    
    Args:
        file_path_or_content: 文件路径、二进制内容、文件对象或UploadFile对象
        
    Returns:
        str: MIME类型
    """
    # 处理UploadFile对象
    if hasattr(file_path_or_content, 'content_type') and file_path_or_content.content_type:
        return file_path_or_content.content_type
    
    mime_type = None
    
    # 尝试使用python-magic
    if HAS_MAGIC:
        try:
            if isinstance(file_path_or_content, str) and os.path.isfile(file_path_or_content):
                # 文件路径
                mime_type = magic.from_file(file_path_or_content, mime=True)
            elif isinstance(file_path_or_content, bytes):
                # 二进制内容
                mime_type = magic.from_buffer(file_path_or_content, mime=True)
            elif hasattr(file_path_or_content, 'read'):
                # 文件对象
                current_position = file_path_or_content.tell()
                file_path_or_content.seek(0)
                content = file_path_or_content.read(4096)  # 读取前4KB判断类型
                file_path_or_content.seek(current_position)  # 恢复位置
                
                if content:
                    mime_type = magic.from_buffer(content, mime=True)
        except Exception as e:
            logger.error(f"使用python-magic获取MIME类型失败: {str(e)}")
    
    # 如果python-magic失败或不可用，使用mimetypes
    if not mime_type:
        if isinstance(file_path_or_content, str):
            # 从文件扩展名判断
            mime_type, _ = mimetypes.guess_type(file_path_or_content)
        elif hasattr(file_path_or_content, 'filename'):
            # UploadFile或有filename属性的对象
            mime_type, _ = mimetypes.guess_type(file_path_or_content.filename)
    
    # 返回默认值，如果无法确定
    return mime_type or 'application/octet-stream'


def get_file_type(mime_type: str) -> FileType:
    """
    根据MIME类型确定文件类型
    
    Args:
        mime_type: MIME类型
        
    Returns:
        FileType: 文件类型枚举值
    """
    mime_type = mime_type.lower()
    
    # 图像类型
    if mime_type.startswith('image/'):
        return FileType.IMAGE
    
    # 视频类型
    elif mime_type.startswith('video/'):
        return FileType.VIDEO
    
    # 音频类型
    elif mime_type.startswith('audio/'):
        return FileType.AUDIO
    
    # 文档类型
    elif any([
        mime_type.startswith('text/'),
        mime_type == 'application/pdf',
        mime_type.startswith('application/vnd.ms-'),
        mime_type.startswith('application/vnd.openxmlformats-'),
        mime_type == 'application/msword',
        'document' in mime_type,
        'excel' in mime_type,
        'powerpoint' in mime_type
    ]):
        return FileType.DOCUMENT
    
    # 压缩文件
    elif any([
        mime_type == 'application/zip',
        mime_type == 'application/x-rar-compressed',
        mime_type == 'application/x-tar',
        mime_type == 'application/gzip',
        mime_type == 'application/x-7z-compressed',
        'compressed' in mime_type,
        'archive' in mime_type
    ]):
        return FileType.ARCHIVE
    
    # 其他类型
    return FileType.OTHER


def guess_file_extension(mime_type: str, file_name: Optional[str] = None) -> str:
    """
    根据MIME类型和文件名推断文件扩展名
    
    Args:
        mime_type: MIME类型
        file_name: 原始文件名（可选）
        
    Returns:
        str: 文件扩展名（包含点）
    """
    # 如果有文件名，先尝试从文件名中提取扩展名
    if file_name:
        _, ext = os.path.splitext(file_name)
        if ext:
            return ext
    
    # 使用mimetypes获取扩展名
    ext = mimetypes.guess_extension(mime_type)
    
    # 一些常见类型的映射修正
    if not ext:
        if mime_type == 'image/jpeg':
            ext = '.jpg'
        elif mime_type == 'image/png':
            ext = '.png'
        elif mime_type == 'application/pdf':
            ext = '.pdf'
        elif mime_type == 'text/plain':
            ext = '.txt'
        elif mime_type == 'application/json':
            ext = '.json'
        elif mime_type == 'application/javascript':
            ext = '.js'
    
    return ext or ''


def build_storage_path(
    file_type: FileType,
    file_hash: str, 
    file_name: Optional[str] = None,
    for_oss: bool = False,
    absolute: bool = False
) -> str:
    """
    构建文件存储路径
    
    Args:
        file_type: 文件类型
        file_hash: 文件哈希值
        file_name: 文件名（用于推断扩展名）
        for_oss: 是否为OSS存储路径（增加/ai/前缀）
        absolute: 是否返回绝对路径
        
    Returns:
        str: 存储路径(相对路径或绝对路径)
    """
    # 确定扩展名
    mime_type = get_mime_type(file_name) if file_name else None
    extension = guess_file_extension(mime_type or "", file_name)

    # 构建相对路径
    relative_path = f"{file_type.value}/{file_hash}{extension}"
    
    # 如果是OSS路径，添加/ai/前缀
    if for_oss:
        relative_path = f"ai/{relative_path}"
    
    # 如果需要绝对路径，转换为绝对路径
    if absolute and not for_oss:
        return get_storage_file_path(relative_path, absolute=True)
    
    # 返回相对路径（如果是非OSS路径，添加FILE_STORAGE_PATH前缀）
    if not for_oss:
        return os.path.join(settings.FILE_STORAGE_PATH, relative_path)
        
    return relative_path



def to_absolute_path(path: str) -> str:
    """
    将路径转换为绝对路径
    
    Args:
        path: 文件或目录路径，可能是相对路径或绝对路径
        
    Returns:
        str: 绝对路径
    """
    # 如果已经是绝对路径，直接返回
    if os.path.isabs(path):
        return path
    
    # 相对路径转换为绝对路径
    return os.path.join(settings.STORAGE_PATH, path)


def to_relative_path(path: str) -> str:
    """
    将路径转换为相对路径（相对于STORAGE_PATH）
    
    Args:
        path: 文件或目录路径，可能是相对路径或绝对路径
        
    Returns:
        str: 相对路径，如果无法转换则返回原路径
    """
    # 如果已经是相对路径，且不以存储路径开头，直接返回
    if not os.path.isabs(path):
        if not path.startswith(settings.FILE_STORAGE_PATH) and not path.startswith(settings.PLUGIN_STORAGE_PATH):
            return path
    
    # 尝试转换为相对路径
    try:
        # 如果是绝对路径，尝试转换为相对于STORAGE_PATH的路径
        if os.path.isabs(path):
            return os.path.relpath(path, settings.STORAGE_PATH)
        return path
    except ValueError:
        # 如果路径不在STORAGE_PATH下，返回原路径
        return path


def get_storage_file_path(relative_path: str, absolute: bool = False) -> str:
    """
    获取存储文件的路径
    
    Args:
        relative_path: 相对于存储目录的路径
        absolute: 是否返回绝对路径
        
    Returns:
        str: 文件路径
    """
    if absolute:
        return os.path.join(settings.STORAGE_PATH, relative_path)
    return relative_path


def get_file_path(file_path: str, absolute: bool = True) -> str:
    """
    获取文件路径，根据需要返回绝对路径或相对路径
    
    Args:
        file_path: 文件路径，可能是相对路径或绝对路径
        absolute: 是否返回绝对路径
        
    Returns:
        str: 处理后的文件路径
    """
    if absolute:
        return to_absolute_path(file_path)
    return to_relative_path(file_path)


def get_plugin_path(plugin_code: str, plugin_version: str, is_deleted: bool = False, absolute: bool = True) -> str:
    """
    获取插件文件路径
    
    Args:
        plugin_code: 插件编码
        plugin_version: 插件版本
        is_deleted: 是否已删除
        absolute: 是否返回绝对路径
        
    Returns:
        str: 插件文件路径
    """
    # 构建版本目录名
    version_dir = f"{plugin_version}-deleted" if is_deleted else plugin_version
    
    # 构建相对路径
    rel_path = os.path.join(settings.PLUGIN_STORAGE_PATH, plugin_code, version_dir)

    # 根据需要返回绝对路径或相对路径
    if absolute:
        return os.path.join(settings.STORAGE_PATH, rel_path)
    return rel_path

def get_plugin_temp_path() -> str:
    """
    获取插件temp文件路径

    Returns:
        str: 插件文件路径
    """
    # 构建相对路径
    rel_path = os.path.join(settings.PLUGIN_STORAGE_PATH, "temp")
    return to_absolute_path(rel_path)



def _get_oss_domain() -> str:
    """
    获取标准化的OSS域名（移除协议前缀）

    Returns:
        str: 标准化的OSS域名，如：oss-cn-shanghai.aliyuncs.com
    """
    endpoint = settings.OSS_ENDPOINT
    # 移除协议前缀
    if endpoint.startswith('https://'):
        endpoint = endpoint[8:]
    elif endpoint.startswith('http://'):
        endpoint = endpoint[7:]
    return endpoint


def _get_project_oss_domain() -> str:
    """
    获取当前项目的完整OSS域名

    Returns:
        str: 项目OSS域名，如：insky-data.oss-cn-shanghai.aliyuncs.com
    """
    oss_domain = _get_oss_domain()
    return f"{settings.OSS_BUCKET_NAME}.{oss_domain}"


def is_oss_url(url: str) -> bool:
    """
    判断URL是否为当前项目配置的OSS桶URL

    Args:
        url: 要检查的URL

    Returns:
        bool: 如果是当前项目的OSS URL返回True，否则返回False
    """
    try:
        parsed_url = urlparse(url)
        project_oss_domain = _get_project_oss_domain()

        # 只匹配当前项目的OSS桶域名
        return parsed_url.netloc == project_oss_domain
    except Exception as e:
        logger.error(f"判断OSS URL失败: {str(e)}")
        return False


def extract_oss_object_key(url: str) -> Optional[str]:
    """
    从OSS URL中提取对象键(object key)

    Args:
        url: OSS URL

    Returns:
        Optional[str]: 对象键，如果提取失败返回None
    """
    try:
        if not is_oss_url(url):
            return None

        parsed_url = urlparse(url)
        # 移除开头的斜杠，获取对象键
        object_key = parsed_url.path.lstrip('/')
        # 解码URL编码的字符
        object_key = unquote(object_key)

        return object_key if object_key else None
    except Exception as e:
        logger.error(f"提取OSS对象键失败: {str(e)}")
        return None


def process_oss_url(url: str) -> str:
    """
    处理OSS URL，将STS临时URL转换为原始URL

    Args:
        url: 原始URL或STS临时URL

    Returns:
        str: 原始OSS URL
    """
    try:
        parsed_url = urlparse(url)

        # 检查是否是OSS URL
        if parsed_url.netloc.endswith(settings.OSS_ENDPOINT):
            # 解码路径并移除查询参数
            path = unquote(parsed_url.path)
            # 保留原始协议（http/https），若无协议则默认使用https
            scheme = parsed_url.scheme or 'https'

            # 重构URL时保留端口信息（如果有）
            netloc = parsed_url.netloc
            if parsed_url.port:
                netloc = f"{parsed_url.hostname}:{parsed_url.port}"

            return f"{scheme}://{netloc}{path}"

        return url
    except Exception as e:
        logger.error(f"处理OSS URL失败: {str(e)}")
        return url


async def extract_metadata(file_path: str, file_type: Union[FileType, str]) -> Optional[Dict[str, Any]]:
    """
    提取文件元数据
    
    Args:
        file_path: 文件路径
        file_type: 文件类型
        
    Returns:
        Optional[Dict[str, Any]]: 元数据字典，如果无法提取则返回 None
    """
    # 确保file_type是枚举实例
    if isinstance(file_type, str):
        file_type = FileType(file_type)
    
    # 检查是否为绝对路径，如果不是则转换为绝对路径
    file_path = to_absolute_path(file_path)
        
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.warning(f"文件不存在，无法提取元数据: {file_path}")
        return None
    
    metadata = None
    
    # 根据文件类型提取不同的元数据
    if file_type == FileType.IMAGE:
        metadata = await extract_image_metadata(file_path)
    elif file_type == FileType.VIDEO:
        # TODO: 提取视频元数据
        pass
    elif file_type == FileType.AUDIO:
        # TODO: 提取音频元数据
        pass
    
    return metadata


async def extract_image_metadata(file_path: str) -> Optional[Dict[str, Any]]:
    """
    提取图片元数据

    Args:
        file_path: 文件本地路径

    Returns:
        Optional[Dict[str, Any]]: 文件元数据，如果提取失败则返回 None
    """
    try:
        # 读取图像元数据
        img = PyexivImage(file_path)

        dict_xmp = img.read_xmp()
        dict_exif = img.read_exif()
        dict_exif['Exif.Photo.MakerNote'] = None

        dict_exif.update(dict_xmp)

        # 转换DMS到十进制
        def dms_to_decimal(dms_str):
            """将度分秒格式的字符串转换为十进制格式"""
            if dms_str is None:
                return None
            try:
                # 分割度、分、秒
                parts = dms_str.split()
                if len(parts) != 3:
                    raise ValueError(f"Invalid DMS format: {dms_str}")

                degrees = Fraction(parts[0].split('/')[0]) / Fraction(parts[0].split('/')[1]) if '/' in parts[0] else float(parts[0])
                minutes = Fraction(parts[1].split('/')[0]) / Fraction(parts[1].split('/')[1]) if '/' in parts[1] else float(parts[1])
                seconds = Fraction(parts[2].split('/')[0]) / Fraction(parts[2].split('/')[1]) if '/' in parts[2] else float(parts[2])

                decimal = degrees + (minutes / 60.0) + (seconds / 3600.0)
                return float(decimal)
            except Exception as e:
                logger.error(f"转换DMS错误: {dms_str}, 错误: {e}")
                return dms_str


        def convert_to_decimal(value):
            if value is None:
                return None
            if '/' in value:
                upper, down = value.split('/')
                res = float(upper) / float(down)
            else:
                res = float(value)
            return str(res)

        with open(file_path, "rb") as f:
            tags = exifread.process_file(f)

            # 定义最终要返回的字段及其处理方式
            imageWidth_get=dict_exif.get("Exif.Photo.PixelXDimension", None)
            imageWidth=imageWidth_get+" pixels" if imageWidth_get else None

            focalLength35_get = dict_exif.get("Exif.Photo.FocalLengthIn35mmFilm", None)
            focalLength35=focalLength35_get+" mm" if focalLength35_get else None

            exifImageWidth_get = dict_exif.get("Exif.Photo.PixelXDimension", None)
            exifImageWidth=exifImageWidth_get+" pixels" if exifImageWidth_get else None

            exifImageHeight_get = dict_exif.get("Exif.Photo.PixelYDimension", None)
            exifImageHeight=exifImageHeight_get+" pixels" if exifImageHeight_get else None

            imageHeight_get = dict_exif.get("Exif.Photo.PixelYDimension", None)
            imageHeight=imageHeight_get+" pixels" if imageHeight_get else None

            focalLength_get = dict_exif.get("Exif.Photo.FocalLength", None)
            focalLength=convert_to_decimal(focalLength_get) +" mm" if focalLength_get else None

            gpsAltitude_get = dict_exif.get("Exif.GPSInfo.GPSAltitude", None)
            gpsAltitude=convert_to_decimal(gpsAltitude_get) +" metres" if gpsAltitude_get else None

            gpsLatitude_get = dict_exif.get("Exif.GPSInfo.GPSLatitude", None)
            gpsLatitude=dms_to_decimal(gpsLatitude_get) if gpsLatitude_get else None

            final_fields = {
                "make": dict_exif.get("Exif.Image.Make", None),
                "model": dict_exif.get("Exif.Image.Model", None),
                "datetime": dict_exif.get("Exif.Image.DateTime", None),
                "gpsStatus": dict_exif.get("Exif.GPSInfo.GPSStatus", None),
                "droneModel": dict_exif.get("Xmp.tiff.Model", None),
                "colorSpace": str(tags.get("EXIF ColorSpace")) if tags.get("EXIF ColorSpace") else None,
                "xmpGpsLatitude": dms_to_decimal(dict_exif.get("Exif.GPSInfo.GPSLatitude", None)),
                "imageWidth":  imageWidth,
                "imageSource": dict_exif.get("Xmp.drone-dji.ImageSource", None),
                "orientation": str(tags.get("Image Orientation")) if tags.get("Image Orientation") else None,
                "altitudeType": dict_exif.get("Xmp.drone-dji.AltitudeType", None),
                "flightXSpeed": dict_exif.get("Xmp.drone-dji.FlightXSpeed", None),
                "flightYSpeed": dict_exif.get("Xmp.drone-dji.FlightYSpeed", None),
                "flightZSpeed": dict_exif.get("Xmp.drone-dji.FlightZSpeed", None),
                "focalLength": focalLength,
                "gpsAltitude": gpsAltitude,
                "gpsLatitude": gpsLatitude,
                "xmpGpsLongitude": dms_to_decimal(dict_exif.get("Exif.GPSInfo.GPSLongitude")) if dict_exif.get("Exif.GPSInfo.GPSLongitude") else None,
                "imageHeight": imageHeight,
                "gpsLongitude": dms_to_decimal(dict_exif.get("Exif.GPSInfo.GPSLongitude")) if dict_exif.get("Exif.GPSInfo.GPSLongitude") else None,
                "gpsMapDatum": dict_exif.get("Exif.GPSInfo.GPSMapDatum", None),
                "gimbalReverse": dict_exif.get("Xmp.drone-dji.GimbalReverse", None),
                "flightYawDegree": dict_exif.get("Xmp.drone-dji.FlightYawDegree", None),
                "focalLength35": focalLength35,
                "gimbalYawDegree": dict_exif.get("Xmp.drone-dji.GimbalYawDegree", None),
                "absoluteAltitude": dict_exif.get("Xmp.drone-dji.AbsoluteAltitude", None),
                "exifImageWidth": exifImageWidth,
                "flightRollDegree": dict_exif.get("Xmp.drone-dji.FlightRollDegree", None),
                "gpsAltitudeRef": dict_exif.get("Exif.GPSInfo.GPSAltitudeRef", None),
                "gimbalRollDegree": dict_exif.get("Xmp.drone-dji.GimbalRollDegree", None),
                "relativeAltitude": dict_exif.get("Xmp.drone-dji.RelativeAltitude", None),
                "droneSerialNumber": dict_exif.get("Exif.Photo.BodySerialNumber", None),
                "exifImageHeight": exifImageHeight,
                "flightPitchDegree": dict_exif.get("Xmp.drone-dji.FlightPitchDegree", None),
                "gimbalPitchDegree": dict_exif.get("Xmp.drone-dji.GimbalPitchDegree", None),
                "imageDescription": dict_exif.get("Exif.Image.ImageDescription", None),
                "cameraSerialNumber": dict_exif.get("Exif.Photo.BodySerialNumber", None),
                "digitalZoomRatio": convert_to_decimal(dict_exif.get("Exif.Photo.DigitalZoomRatio")) if dict_exif.get("Exif.Photo.DigitalZoomRatio") else None
            }

            # 过滤掉所有值为 None 的字段
            final_fields = {k: v for k, v in final_fields.items() if v is not None}
            
            # 如果没有提取到任何有效的元数据，返回 None
            return final_fields if final_fields else None

    except Exception as e:
        logger.error(f"提取图像元数据失败: {str(e)}")
        return None



