"""
结果缓存模型
"""
from datetime import datetime

from sqlalchemy import (
    Column,
    DateTime,
    String,
    BigInteger,
    Integer,
    Index,
    func,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB

from app.db.base import Base


class ResultCache(Base):
    """
    结果缓存表：存储推理结果缓存
    """
    __tablename__ = "result_cache"

    id = Column(BigInteger, primary_key=True, comment="雪花ID，缓存唯一标识")
    file_hash = Column(String(64), nullable=False, comment="文件哈希")
    plugin_code = Column(String(100), nullable=False, comment="插件编码")
    plugin_version = Column(String(20), nullable=False, comment="插件版本")
    params_hash = Column(String(64), nullable=False, comment="参数哈希")
    result = Column(JSONB, nullable=False, comment="缓存结果")
    process_time = Column(JSONB, comment="处理各阶段时间")
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 唯一约束和索引
    __table_args__ = (
        UniqueConstraint("file_hash", "plugin_code", "plugin_version", "params_hash", name="uix_cache_file_plugin_params"),
        Index("idx_result_cache_composite", "file_hash", "plugin_code", "plugin_version", "params_hash", unique=True),
    ) 