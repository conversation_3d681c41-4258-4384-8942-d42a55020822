"""
插件模型
"""
from datetime import datetime
from typing import Optional

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    String,
    BigInteger,
    Text,
    Index,
    func,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB

from app.db.base import Base


class Plugin(Base):
    """
    插件表：存储插件元数据和文件路径信息
    """
    __tablename__ = "plugin"

    id = Column(BigInteger, primary_key=True, comment="雪花ID，插件唯一标识")
    plugin_code = Column(String(100), nullable=False, comment="插件唯一编码")
    plugin_version = Column(String(20), nullable=False, comment="插件版本号")
    name = Column(String(100), nullable=False, comment="插件名称")
    description = Column(Text, comment="插件描述")
    type = Column(String(50), nullable=False, comment="插件类型，如quantization/detection/mosaic")
    engine = Column(String(50), nullable=False, comment="推理引擎类型，如onnx/pytorch/opencv/api")
    input_file_type = Column(JSONB, nullable=False, comment="支持的输入文件类型")
    input_media_type = Column(JSONB, nullable=False, comment="支持的输入媒体类型")
    classes = Column(JSONB, nullable=False, comment="支持的类别列表")
    author = Column(String(100), comment="作者")
    status = Column(String(20), nullable=False, default="enabled", comment="状态")
    model_file_path = Column(Text, nullable=True, comment="模型文件路径")
    config_file_path = Column(Text, nullable=False, comment="配置文件路径")
    python_file_path = Column(Text, nullable=False, comment="Python处理逻辑文件路径")
    is_deleted = Column(Boolean, nullable=False, default=False, comment="逻辑删除标记")
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 索引
    __table_args__ = (
        UniqueConstraint("plugin_code", "plugin_version", "is_deleted", name="uix_plugin_code_version_delete"),
        Index("idx_plugin_status", "status"),
        Index("idx_plugin_is_deleted", "is_deleted"),
    ) 