"""
文件下载服务
负责从URL或本地路径下载文件，提取元数据，并保存到本地存储
"""
import os
import asyncio
import json
from typing import Optional, Dict, List, Tuple, Any
from urllib.parse import urlparse, unquote
from datetime import datetime

from fastapi import HTTPException, status

from app.config.settings import settings
from app.schemas.file import FileType, MediaType, FileCreate, File
from app.core.crud.file import file_crud
from app.db.session import get_async_db
from app.utils.file import calculate_file_hash, get_mime_type, get_file_type, build_storage_path, extract_metadata, \
    process_oss_url, to_absolute_path, to_relative_path, is_oss_url, extract_oss_object_key
from app.log import file_logger as logger

# 延迟导入，防止循环导入
import httpx
from app.utils.oss import oss_client


class FileDownloader:
    """文件下载服务"""
    
    def __init__(self):
        """初始化文件下载服务"""
        self.client = None
        self.client_timeout = settings.HTTP_CLIENT_TIMEOUT

    async def _download_file(self, url: str, retry_with_sts: bool = True) -> Tuple[bytes, Optional[str]]:
        """
        下载文件内容，支持OSS 403错误的STS重试

        Args:
            url: 文件URL
            retry_with_sts: 是否在403错误时使用STS重试

        Returns:
            Tuple[bytes, Optional[str]]: 文件内容和content-type
        """
        try:
            async with httpx.AsyncClient(timeout=self.client_timeout) as client:
                response = await client.get(url)
                response.raise_for_status()
                return response.content, response.headers.get('content-type')

        except httpx.HTTPStatusError as e:
            # 检查是否为403错误且是OSS URL，如果是则尝试STS重试
            if (e.response.status_code == 403 and
                retry_with_sts and
                is_oss_url(url)):

                logger.warning(f"OSS文件下载遇到403错误，尝试使用STS令牌重试: {url}")
                return await self._download_with_sts_retry(url)

            # 其他HTTP错误直接抛出
            logger.error(f"下载文件HTTP错误 {e.response.status_code}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文件下载失败: HTTP {e.response.status_code}"
            )

        except Exception as e:
            logger.error(f"下载文件失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文件下载失败: {str(e)}"
            )

    async def _download_with_sts_retry(self, url: str) -> Tuple[bytes, Optional[str]]:
        """
        使用STS令牌重试下载OSS文件

        Args:
            url: OSS文件URL

        Returns:
            Tuple[bytes, Optional[str]]: 文件内容和content-type

        Raises:
            HTTPException: 重试失败时抛出
        """
        try:
            # 提取OSS对象键
            object_key = extract_oss_object_key(url)
            if not object_key:
                raise ValueError(f"无法从URL提取OSS对象键: {url}")

            logger.info(f"为OSS对象生成STS令牌: {object_key}")

            # 生成STS令牌
            sts_token, expiration = await oss_client.generate_sts_token(
                duration_seconds=3600  # 1小时有效期
            )

            # 使用STS令牌生成预签名URL
            presigned_url, expires_at = await oss_client.generate_presigned_url(
                object_key=object_key,
                expires_in=3600,
                sts_token=sts_token
            )

            logger.info(f"使用STS预签名URL重试下载: {presigned_url}")

            # 使用新URL重试下载（禁用进一步的STS重试，避免无限循环）
            return await self._download_file(presigned_url, retry_with_sts=False)

        except Exception as e:
            logger.error(f"STS重试下载失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"STS重试下载失败: {str(e)}"
            )

    async def download_from_url(
        self,
        url: str,
        file_type: Optional[FileType] = None,
        save_to_storage: bool = True,
        file_name: Optional[str] = None,
    ) -> File:
        """
        从URL下载文件
        
        Args:
            url: 文件URL
            file_type: 文件类型
            save_to_storage: 是否保存到永久存储
            file_name: 文件名
            
        Returns:
            File: 文件信息
        """
        try:
            # 1. 处理URL，获取原始OSS URL
            original_url = process_oss_url(url)
            
            # 获取或推断文件名
            if not file_name:
                parsed_url = urlparse(original_url)
                file_name = os.path.basename(unquote(parsed_url.path))
                if not file_name:
                    file_name = f"file_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # 2. 查询数据库中是否存在记录
            async with get_async_db() as session:
                existing_file = await file_crud.get_by_url(session, original_url)

                # 3. 处理已存在的记录
                if existing_file and not existing_file.is_deleted:

                    # 检查本地文件是否存在
                    if existing_file.local_path and os.path.exists(to_absolute_path(existing_file.local_path)):
                        logger.info(f"文件已存在，直接返回: {original_url}")
                        return File(
                            id=existing_file.id,
                            size=existing_file.size,
                            file_type=existing_file.file_type,
                            media_type=existing_file.media_type,
                            file_metadata=existing_file.file_metadata,  # 使用 file_metadata
                            content_type=existing_file.content_type,
                            url=existing_file.url,
                            file_hash=existing_file.file_hash,
                            local_path=existing_file.local_path,
                            oss_path=existing_file.oss_path,
                            created_at=existing_file.created_at,
                            updated_at=existing_file.updated_at
                        )

                    # 本地文件不存在，重新下载
                    content, content_type = await self._download_file(url)
                    
                    # 生成本地存储路径
                    if not existing_file.local_path:
                        # 将字符串转换为枚举实例
                        existing_file_type = FileType(existing_file.file_type)
                        
                        # 获取相对路径
                        relative_path = build_storage_path(
                            file_type=existing_file_type,
                            file_hash=existing_file.file_hash,
                            file_name=file_name,
                            for_oss=False,
                            absolute=False
                        )
                        # 存储相对路径
                        db_local_path = relative_path
                        # 使用绝对路径进行文件操作
                        full_local_path = to_absolute_path(relative_path)
                    else:
                        # 如果已有路径是相对路径，转换为绝对路径用于文件操作
                        if not os.path.isabs(existing_file.local_path):
                            db_local_path = existing_file.local_path
                            full_local_path = to_absolute_path(existing_file.local_path)
                        else:
                            # 如果已有路径是绝对路径，保持不变用于文件操作，但存储为相对路径
                            full_local_path = existing_file.local_path
                            # 尝试转换为相对路径
                            db_local_path = to_relative_path(existing_file.local_path)

                    # 保存文件到本地
                    os.makedirs(os.path.dirname(full_local_path), exist_ok=True)
                    with open(full_local_path, 'wb') as f:
                        f.write(content)

                    # 提取元数据
                    metadata = None
                    if save_to_storage:
                        metadata = await extract_metadata(full_local_path, existing_file.file_type)

                    # 更新文件记录
                    existing_file.local_path = db_local_path  # 保存相对路径
                    existing_file.size = len(content)
                    existing_file.content_type = content_type or existing_file.content_type
                    existing_file.file_metadata = metadata
                    existing_file.updated_at = datetime.now()
                    
                    await session.commit()
                    await session.refresh(existing_file)
                    
                    return File(
                        id=existing_file.id,
                        size=existing_file.size,
                        file_type=existing_file.file_type,
                        media_type=existing_file.media_type,
                        file_metadata=existing_file.file_metadata,
                        content_type=existing_file.content_type,
                        url=existing_file.url,
                        file_hash=existing_file.file_hash,
                        local_path=existing_file.local_path,
                        oss_path=existing_file.oss_path,
                        created_at=existing_file.created_at,
                        updated_at=existing_file.updated_at
                    )

            # 4. 处理新文件
            # 下载文件
            content, content_type = await self._download_file(url)

            # 获取文件类型
            if not file_type:
                mime_type = content_type or get_mime_type(file_name)
                file_type = get_file_type(mime_type)  # 这里已经返回 FileType 枚举实例

            # 计算文件哈希
            file_hash = calculate_file_hash(content)

            # 生成本地存储路径
            relative_path = build_storage_path(
                file_type=file_type,
                file_hash=file_hash,
                file_name=file_name,
                for_oss=False,
                absolute=False
            )
            # 存储相对路径
            db_local_path = relative_path
            # 使用绝对路径进行文件操作
            full_local_path = to_absolute_path(relative_path)

            # 保存文件到本地
            os.makedirs(os.path.dirname(full_local_path), exist_ok=True)
            with open(full_local_path, 'wb') as f:
                f.write(content)

            # 提取元数据
            metadata = None
            if save_to_storage:
                metadata = await extract_metadata(full_local_path, file_type)

            # 创建文件记录
            file_create = FileCreate(
                url=original_url,  # 使用原始URL
                size=len(content),
                file_type=file_type.value,  # 使用枚举值
                file_metadata=metadata,  # 直接使用提取的元数据，可能为 None
                content_type=content_type,
                file_hash=file_hash,
                local_path=db_local_path,  # 使用相对路径
                oss_path="",  # 本地下载的文件没有OSS路径
                platform="aliyun-oss-1",  # 添加必需的 platform 字段
                is_deleted=False
            )

            # 保存到数据库
            async with get_async_db() as session:
                file_db = await file_crud.create_file(session, file_create)
            
            return File(
                id=file_db.id,
                size=file_db.size,
                file_type=file_db.file_type,
                media_type=file_db.media_type,
                file_metadata=file_db.file_metadata,  # 使用 file_metadata
                content_type=file_db.content_type,
                url=file_db.url,
                file_hash=file_db.file_hash,
                local_path=file_db.local_path,
                oss_path=file_db.oss_path,
                created_at=file_db.created_at,
                updated_at=file_db.updated_at
            )
            
        except Exception as e:
            logger.error(f"文件下载失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文件下载失败: {str(e)}"
            )

    async def batch_download_from_urls(
        self, 
        urls: List[str], 
        file_type: Optional[FileType] = None,
        save_to_storage: bool = True
    ) -> List[File]:
        """
        批量从URL下载文件
        
        Args:
            urls: 文件URL列表
            file_type: 文件类型
            save_to_storage: 是否保存到永久存储
            
        Returns:
            List[File]: 文件信息列表
        """
        tasks = []
        for url in urls:
            task = asyncio.create_task(
                self.download_from_url(
                    url=url,
                    file_type=file_type,
                    save_to_storage=save_to_storage
                )
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤掉异常并记录错误
        file_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"下载URL失败 {urls[i]}: {str(result)}")
            else:
                file_results.append(result)
        
        return file_results
    

# 创建全局文件下载器实例
file_downloader = FileDownloader() 