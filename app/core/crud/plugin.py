"""
插件CRUD操作
"""
from typing import Dict, List, Optional, Any, Union

from sqlalchemy import select, or_, and_, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import func

from app.core.crud.base import CRUDBase
from app.models.plugin import Plugin
from app.schemas.plugin import PluginCreate, PluginUpdate, PluginFilter


class CRUDPlugin(CRUDBase[Plugin, PluginCreate, PluginUpdate]):
    """插件CRUD操作类"""
    
    async def get_by_code_version(
        self, db: AsyncSession, *, plugin_code: str, plugin_version: str
    ) -> Optional[Plugin]:
        """
        根据插件编码和版本号获取插件
        
        Args:
            db: 数据库会话
            plugin_code: 插件编码
            plugin_version: 插件版本号
            
        Returns:
            Optional[Plugin]: 插件数据库对象或None
        """
        result = await db.execute(
            select(self.model)
            .where(
                and_(
                    self.model.plugin_code == plugin_code,
                    self.model.plugin_version == plugin_version,
                    self.model.is_deleted == False
                )
            )
        )
        return result.scalars().first()
    
    async def get_by_code(
        self, db: AsyncSession, *, plugin_code: str, exclude_version: Optional[str] = None
    ) -> List[Plugin]:
        """
        根据插件编码获取所有版本的插件
        
        Args:
            db: 数据库会话
            plugin_code: 插件编码
            exclude_version: 需要排除的版本号
            
        Returns:
            List[Plugin]: 插件数据库对象列表
        """
        query = select(self.model).where(
            and_(
                self.model.plugin_code == plugin_code,
                self.model.is_deleted == False
            )
        )
        
        # 如果指定了需要排除的版本，则添加过滤条件
        if exclude_version:
            query = query.where(self.model.plugin_version != exclude_version)
            
        # 按版本创建时间降序排序，保证最新版本在前
        query = query.order_by(self.model.created_at.desc())
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def update_status_by_ids(
        self, db: AsyncSession, *, ids: List[int], status: str
    ) -> int:
        """
        批量更新多个插件的状态
        
        Args:
            db: 数据库会话
            ids: 插件ID列表
            status: 新状态
            
        Returns:
            int: 更新的记录数
        """
        if not ids:
            return 0
            
        # 执行批量更新
        stmt = (
            update(self.model)
            .where(
                and_(
                    self.model.id.in_(ids),
                    self.model.is_deleted == False
                )
            )
            .values(status=status)
        )
        
        result = await db.execute(stmt)
        await db.commit()
        
        # 返回更新的记录数
        return result.rowcount
    
    async def get_filtered(
        self,
        db: AsyncSession,
        *,
        filter_params: PluginFilter,
        skip: int = 0,
        limit: int = 100
    ) -> List[Plugin]:
        """
        根据过滤条件获取插件列表
        
        Args:
            db: 数据库会话
            filter_params: 过滤条件
            skip: 跳过记录数
            limit: 返回记录数上限
            
        Returns:
            List[Plugin]: 插件列表
        """
        query = select(self.model).where(self.model.is_deleted == False)
        
        # 应用过滤条件
        if filter_params.plugin_code:
            query = query.where(self.model.plugin_code.ilike(f"%{filter_params.plugin_code}%"))
        
        if filter_params.name:
            query = query.where(self.model.name.ilike(f"%{filter_params.name}%"))
        
        if filter_params.type:
            query = query.where(self.model.type == filter_params.type)
        
        if filter_params.engine:
            query = query.where(self.model.engine == filter_params.engine)
        
        if filter_params.status:
            query = query.where(self.model.status == filter_params.status)
            
        # 添加对文件类型和媒体类型的过滤
        if filter_params.file_type:
            # 在JSONB数组中查找指定的文件类型
            query = query.where(self.model.input_file_type.contains([filter_params.file_type]))
            
        if filter_params.media_type:
            # 在JSONB数组中查找指定的媒体类型
            query = query.where(self.model.input_media_type.contains([filter_params.media_type]))
        
        # 应用排序（按创建时间降序，ID降序确保稳定性）
        query = query.order_by(self.model.created_at.desc(), self.model.id.desc())
        
        # 应用分页
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def count_filtered(
        self,
        db: AsyncSession,
        *,
        filter_params: PluginFilter
    ) -> int:
        """
        根据过滤条件计算插件数量
        
        Args:
            db: 数据库会话
            filter_params: 过滤条件
            
        Returns:
            int: 符合条件的插件数量
        """
        query = select(func.count()).select_from(self.model).where(self.model.is_deleted == False)
        
        # 应用过滤条件
        if filter_params.plugin_code:
            query = query.where(self.model.plugin_code.ilike(f"%{filter_params.plugin_code}%"))
        
        if filter_params.name:
            query = query.where(self.model.name.ilike(f"%{filter_params.name}%"))
        
        if filter_params.type:
            query = query.where(self.model.type == filter_params.type)
        
        if filter_params.engine:
            query = query.where(self.model.engine == filter_params.engine)
        
        if filter_params.status:
            query = query.where(self.model.status == filter_params.status)
            
        # 添加对文件类型和媒体类型的过滤
        if filter_params.file_type:
            # 在JSONB数组中查找指定的文件类型
            query = query.where(self.model.input_file_type.contains([filter_params.file_type]))
            
        if filter_params.media_type:
            # 在JSONB数组中查找指定的媒体类型
            query = query.where(self.model.input_media_type.contains([filter_params.media_type]))
        
        result = await db.execute(query)
        return result.scalar()
    
    async def create_with_files(
        self,
        db: AsyncSession,
        *,
        obj_in: PluginCreate,
        model_file_path: str,
        config_file_path: str,
        python_file_path: str
    ) -> Plugin:
        """
        创建带文件路径的插件
        
        Args:
            db: 数据库会话
            obj_in: 插件创建数据
            model_file_path: 模型文件路径
            config_file_path: 配置文件路径
            python_file_path: Python处理逻辑文件路径
            
        Returns:
            Plugin: 创建的插件数据库对象
        """
        # 添加文件路径到创建数据中
        obj_in_data = obj_in.model_dump(by_alias=False)
        obj_in_data.update({
            "model_file_path": model_file_path,
            "config_file_path": config_file_path,
            "python_file_path": python_file_path
        })

        # 使用基类的create方法，自动处理ID生成
        return await self.create(db, obj_in=obj_in_data)
    
    async def soft_delete(self, db: AsyncSession, *, db_obj: Plugin) -> Plugin:
        """
        软删除插件
        
        Args:
            db: 数据库会话
            db_obj: 要删除的插件数据库对象
            
        Returns:
            Plugin: 更新后的插件数据库对象
        """
        db_obj.is_deleted = True
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


# 创建插件CRUD实例
plugin_crud = CRUDPlugin(Plugin) 