"""
任务管理器
负责任务的创建、状态更新和查询
"""
from typing import Dict, List, Any, Optional
from datetime import datetime
import os

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, status

from app.models.task import Task, TaskItem
from app.core.crud.task import task_crud
from app.core.crud.task_item import task_item_crud
from app.core.file.manager import file_manager
from app.schemas.file import File
from app.schemas.task import TaskFilter, TaskItemCreate, TaskCreate, TaskResponse, TaskStatus, TaskItemResponse
from app.log import task_logger as logger
from app.utils.file import process_oss_url, to_absolute_path
from app.utils.trace_context_utils import create_task_with_context, traced_async_task, add_span_attributes, serialize_trace_context
from app.schemas.base import MediaType, PageResponse, PageParams


class TaskManager:
    """任务管理器"""

    @staticmethod
    async def create_task(
            db: AsyncSession,
            *,
            task_in: TaskCreate
    ) -> TaskResponse:
        """
        创建新任务
        
        Args:
            db: 数据库会话
            task_in: 任务创建参数
            
        Returns:
            Task: 创建的任务对象
            
        Raises:
            HTTPException: 创建失败时抛出
        """
        try:
            # 插件验证
            if not task_in.plugins or len(task_in.plugins) == 0:
                raise ValueError("未提供插件信息")

            # 验证所有插件是否存在且已启用
            from app.core.crud.plugin import plugin_crud
            for plugin_config in task_in.plugins:
                plugin_code = plugin_config.plugin_code
                plugin_version = plugin_config.plugin_version

                plugin = await plugin_crud.get_by_code_version(
                    db,
                    plugin_code=plugin_code,
                    plugin_version=plugin_version
                )

                if not plugin:
                    raise ValueError(f"插件 {plugin_code} 版本 {plugin_version} 不存在")

                if plugin.status != "enabled":
                    raise ValueError(f"插件 {plugin_code} 版本 {plugin_version} 未启用，当前状态: {plugin.status}")

            # 计算总任务项数量 = 任务项数量 × 插件数量（笛卡尔积）
            total_items_count = len(task_in.items) * len(task_in.plugins)

            # 1. 创建任务记录（初始状态为 initializing）
            task_data = {
                "total_items": total_items_count,
                "status": "initializing",  # 任务项创建中
                "priority": task_in.priority
            }
            # 添加数据库操作的span属性
            add_span_attributes(
                db_operation="task_create",
                total_items=len(task_in.items),
                total_plugins=len(task_in.plugins)
            )
            task = await task_crud.create(db, obj_in=task_data)

            # 启动异步任务处理任务项创建、状态更新和调度队列
            # 使用create_task_with_context确保trace context传播到异步任务
            create_task_with_context(
                TaskManager._process_task_items(
                    task.id,
                    task_in.items,
                    task_in.plugins,
                    task_in.priority
                ),
                name=f"process_task_items_{task.id}"
            )

            # 传入任务对象获取进度
            return await task_manager.get_task_progress(db, task=task)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建任务失败: {str(e)}"
            )

    @staticmethod
    @traced_async_task(
        "task_items_processing_{task_id}",
        attributes={
            "component": "task_manager",
            "operation": "process_task_items"
        }
    )
    async def _process_task_items(
            task_id: int,
            items: List[TaskItemCreate],
            plugins: List[Any],
            priority: int
    ):
        """
        后台处理任务项创建、状态更新和调度队列

        Args:
            task_id: 任务ID
            items: 任务项列表
            plugins: 插件列表
            priority: 任务优先级
        """
        try:
            # 添加span属性用于追踪
            add_span_attributes(
                task_id=task_id,
                total_items=len(items),
                total_plugins=len(plugins),
                priority=priority
            )

            # 创建会话
            from app.db.session import get_async_db
            from app.core.crud.plugin import plugin_crud
            from app.core.task.scheduler import scheduler
            from app.config.settings import settings
            from app.core.task.redis_queue import redis_queue

            # 使用批量创建方式
            task_items_to_create = []
            completed_items_count = 0

            async with get_async_db() as db:
                # 获取任务对象
                task = await task_crud.get(db, id=task_id)
                if not task:
                    logger.error(f"任务不存在: {task_id}")
                    return

                # 2. 预处理所有文件和插件，准备批量创建任务项
                logger.info(f"开始预处理任务 {task_id} 的 {len(items)} 个文件和 {len(plugins)} 个插件")

                # 预加载所有启用的插件信息，避免在循环中重复查询
                enabled_plugins = await plugin_crud.get_multi(
                    db,
                    filters={"status": "enabled"}
                )

                # 构建插件缓存字典 {plugin_code:plugin_version -> plugin_obj}
                plugin_cache = {}
                for plugin in enabled_plugins:
                    plugin_key = f"{plugin.plugin_code}:{plugin.plugin_version}"
                    plugin_cache[plugin_key] = plugin

                logger.info(f"预加载了 {len(plugin_cache)} 个启用的插件")

                # ========================================
                # 性能优化：批量预加载文件和缓存信息
                # ========================================

                # 1. 收集所有唯一的文件URL
                unique_urls = list(set(process_oss_url(item.file_url) for item in items))
                logger.info(f"批量预加载 {len(unique_urls)} 个唯一文件URL")

                # 2. 批量查询文件信息
                from app.core.crud.file import file_crud
                try:
                    files_map = await file_crud.get_by_urls_batch(db, unique_urls)
                    logger.info(f"批量查询到 {len(files_map)} 个文件记录")
                except Exception as e:
                    logger.error(f"批量查询文件失败: {str(e)}")
                    files_map = {}

                # 3. 收集所有唯一的缓存键
                cache_keys = []
                for item in items:
                    file_url = process_oss_url(item.file_url)
                    if file_url in files_map:
                        file_obj = files_map[file_url]
                        for plugin_config in plugins:
                            # 计算参数哈希
                            from app.utils.hash import calculate_params_hash
                            params_hash = calculate_params_hash(item.params)
                            cache_keys.append((file_obj.file_hash, plugin_config.plugin_code, plugin_config.plugin_version, params_hash))

                unique_cache_keys = list(set(cache_keys))
                logger.info(f"批量预加载 {len(unique_cache_keys)} 个唯一缓存键")

                # 4. 批量查询缓存信息
                from app.core.crud.result_cache import result_cache_crud
                caches_map = await result_cache_crud.get_by_composite_keys_batch(db, unique_cache_keys)
                logger.info(f"批量查询到 {len(caches_map)} 个缓存记录")

                # ========================================
                # 处理每个文件和插件的组合（使用预加载的数据）
                # ========================================
                for item in items:
                    # 从预加载的数据中获取文件信息
                    file = None
                    file_download_error = None
                    processed_url = process_oss_url(item.file_url)


                    if processed_url in files_map:
                        # 文件已存在于数据库中，但需要检查本地文件是否存在
                        file_db = files_map[processed_url]

                        # 检查本地文件是否存在
                        local_file_exists = (
                            file_db.local_path and
                            os.path.exists(to_absolute_path(file_db.local_path))
                        )

                        if local_file_exists:
                            # 本地文件存在，直接使用数据库记录
                            logger.info(f"文件已存在，直接使用: {processed_url}")
                            file = File(
                                id=file_db.id,
                                size=file_db.size,
                                file_type=file_db.file_type,
                                media_type=file_db.media_type,
                                file_metadata=file_db.file_metadata,
                                content_type=file_db.content_type,
                                url=file_db.url,
                                file_hash=file_db.file_hash,
                                local_path=file_db.local_path,
                                oss_path=file_db.oss_path,
                                created_at=file_db.created_at,
                                updated_at=file_db.updated_at
                            )
                        else:
                            # 本地文件不存在，需要重新下载
                            logger.info(f"数据库记录存在但本地文件缺失，重新下载: {item.file_url}")
                            try:
                                file = await file_manager.download_from_url(
                                    url=item.file_url,
                                    file_type=item.file_type,
                                    save_to_storage=True
                                )
                            except Exception as e:
                                file_download_error = str(e)
                                logger.error(f"重新下载文件失败: {file_download_error}")
                    else:
                        # 文件不存在于数据库中，需要下载
                        logger.info(f"文件不存在于数据库，需要下载: {item.file_url}")
                        try:
                            file = await file_manager.download_from_url(
                                url=item.file_url,
                                file_type=item.file_type,
                                save_to_storage=True
                            )
                        except Exception as e:
                            file_download_error = str(e)
                            logger.error(f"下载文件失败: {file_download_error}")

                    # ---------------------------------
                    # 媒体类型判断和过滤逻辑
                    # 1. 根据文件元数据判断媒体类型:
                    #    - 如果元数据为空，则判定为 visible_light
                    #    - 如果元数据中 imageSource 为空，则判定为 visible_light
                    #    - 如果元数据中 imageSource 为 ZoomCamera，则判定为 visible_light
                    #    - 如果元数据中 imageSource 为 InfraredCamera，则判定为 infrared_light
                    # 2. 根据插件支持的媒体类型进行过滤:
                    #    - 只有当文件的媒体类型在插件支持的媒体类型列表中时，才创建任务项
                    # ---------------------------------

                    # 根据文件元数据决定媒体类型
                    file_media_type = MediaType.VISIBLE_LIGHT.value  # 默认为可见光

                    if file and file.file_metadata and isinstance(file.file_metadata, dict):
                        # 获取 imageSource 字段
                        image_source = file.file_metadata.get('imageSource')
                        if image_source == 'InfraredCamera':
                            file_media_type = MediaType.INFRARED_LIGHT.value
                            logger.info(f"检测到红外光图像: {file.local_path}, imageSource={image_source}")
                        elif image_source == 'ZoomCamera' or not image_source:
                            file_media_type = MediaType.VISIBLE_LIGHT.value
                            logger.info(f"检测到可见光图像: {file.local_path}, imageSource={image_source}")
                    elif file:
                        logger.info(f"文件元数据为空，使用默认媒体类型(可见光): {file.local_path}")

                    # 为每个插件创建任务项数据
                    for plugin_config in plugins:
                        plugin_code = plugin_config.plugin_code
                        plugin_version = plugin_config.plugin_version

                        # 初始化任务项数据的公共部分
                        task_item_data = {
                            "task_id": task_id,
                            "data_id": item.data_id,
                            "file_type": item.file_type,
                            "media_type": file_media_type,
                            "file_url": processed_url,
                            "plugin_code": plugin_code,
                            "plugin_version": plugin_version,
                            "params": item.params,
                            "from_cache": False,
                        }

                        # 如果文件下载失败，添加失败状态的任务项到批量创建列表
                        if file_download_error:
                            error_message = f"文件下载失败: {file_download_error}"
                            task_item_data.update({
                                "status": "failed",
                                "error": error_message,
                                "file_id": None
                            })
                            task_items_to_create.append(task_item_data)
                            completed_items_count += 1  # 失败的任务项也算作已完成
                            continue

                        # 文件下载成功，继续正常处理流程
                        task_item_data["file_id"] = file.id
                        task_item_data["file_hash"] = file.file_hash

                        # 从缓存中获取插件信息
                        plugin_key = f"{plugin_code}:{plugin_version}"
                        plugin = plugin_cache.get(plugin_key)

                        # 判断插件是否存在
                        if not plugin:
                            error_message = f"插件 {plugin_code}:{plugin_version} 不存在"
                            logger.warning(error_message)

                            # 添加失败状态的任务项到批量创建列表
                            task_item_data.update({
                                "status": "failed",
                                "error": error_message
                            })
                            task_items_to_create.append(task_item_data)
                            completed_items_count += 1  # 失败的任务项也算作已完成
                            continue

                        # 判断插件是否启用
                        if plugin.status != "enabled":
                            error_message = f"插件 {plugin_code}:{plugin_version} 未启用，当前状态: {plugin.status}"
                            logger.warning(error_message)

                            # 添加失败状态的任务项到批量创建列表
                            task_item_data.update({
                                "status": "failed",
                                "error": error_message
                            })
                            task_items_to_create.append(task_item_data)
                            completed_items_count += 1  # 失败的任务项也算作已完成
                            continue

                        # 检查文件的媒体类型是否在插件支持的媒体类型列表中
                        plugin_media_types = plugin.input_media_type
                        # 确保是列表类型
                        if not isinstance(plugin_media_types, list):
                            logger.warning(
                                f"插件 {plugin_code}:{plugin_version} 的媒体类型不是列表格式: {plugin_media_types}")
                            plugin_media_types = []

                        if not plugin_media_types or file_media_type not in plugin_media_types:
                            error_message = f"媒体类型不匹配: 文件={file.file_hash}, 媒体类型={file_media_type}, 插件={plugin_code}:{plugin_version}, 支持的媒体类型={plugin_media_types}"
                            logger.info(error_message)

                            # 添加失败状态的任务项到批量创建列表
                            task_item_data.update({
                                "status": "failed",
                                "error": error_message
                            })
                            task_items_to_create.append(task_item_data)
                            completed_items_count += 1  # 失败的任务项也算作已完成
                            continue

                        # 从预加载的缓存数据中查找
                        from app.utils.hash import calculate_params_hash
                        params_hash = calculate_params_hash(item.params)
                        cache_key = f"{file.file_hash}:{plugin_code}:{plugin_version}:{params_hash}"

                        cache = None
                        if cache_key in caches_map:
                            cache_obj = caches_map[cache_key]
                            cache = {
                                "result": cache_obj.result,
                                "process_time": cache_obj.process_time
                            }

                        # 更新任务项数据
                        task_item_data.update({
                            "status": "completed" if cache else "pending",
                            "from_cache": bool(cache),
                            "result": cache.get("result") if cache else None,
                            "process_time": cache.get("process_time") if cache else None
                        })

                        # 如果有缓存命中，在process_time中添加缓存命中时间信息
                        if cache:
                            current_time = datetime.now()
                            # 对于缓存命中的情况，添加时间戳，直接使用 datetime 对象
                            task_item_data["started_at"] = current_time
                            task_item_data["completed_at"] = current_time

                            logger.info(f"缓存命中: 文件哈希={file.file_hash}, 插件={plugin_code}:{plugin_version}")

                            completed_items_count += 1  # 缓存命中的任务项算作已完成

                        # 添加任务项到批量创建列表
                        task_items_to_create.append(task_item_data)

                # 3. 批量创建所有任务项（原子操作）
                logger.info(f"开始批量创建任务 {task_id} 的 {len(task_items_to_create)} 个任务项")
                if task_items_to_create:
                    try:
                        # 添加批量操作的span属性
                        add_span_attributes(
                            db_operation="task_items_batch_create",
                            batch_size=len(task_items_to_create),
                            task_id=task_id
                        )
                        created_items = await task_item_crud.create_batch(db, objs_in=task_items_to_create)
                        logger.info(f"成功批量创建 {len(created_items)} 个任务项")
                    except Exception as e:
                        logger.error(f"批量创建任务项失败: {str(e)}")
                        # 如果批量创建失败，标记任务为失败状态
                        await task_crud.update(db, db_obj=task, obj_in={
                            "status": "failed",
                            "error": f"任务项创建失败: {str(e)}",
                            "completed_at": datetime.now()
                        })
                        return

                # 4. 检查任务项创建完整性并更新任务状态
                expected_total_items = task.total_items  # 保持原始预期数量
                actual_created_items = len(task_items_to_create)
                pending_items = actual_created_items - completed_items_count

                # 检查是否所有预期的任务项都被创建
                if actual_created_items < expected_total_items:
                    # 任务项创建不完整，记录警告
                    missing_items = expected_total_items - actual_created_items
                    logger.warning(
                        f"任务 {task_id} 任务项创建不完整: "
                        f"预期 {expected_total_items} 个，实际创建 {actual_created_items} 个，"
                        f"缺失 {missing_items} 个任务项"
                    )

                # 根据待处理项数量决定任务状态
                task_update_data = {}
                if pending_items <= 0:
                    # 所有已创建的任务项都已完成（包括缓存命中和失败的）
                    task_update_data.update({
                        "status": "completed",
                        "completed_at": datetime.now()
                    })
                    logger.info(f"任务 {task_id} 所有已创建的 {actual_created_items} 个任务项已完成，状态更新为 completed")
                else:
                    # 有待处理的任务项，状态更新为 pending
                    task_update_data["status"] = "pending"
                    logger.info(f"任务 {task_id} 创建了 {actual_created_items} 个任务项，其中 {pending_items} 个待处理，状态更新为 pending")

                # 更新任务
                add_span_attributes(
                    db_operation="task_status_update",
                    task_id=task_id,
                    new_status=task_update_data.get("status", "unknown"),
                    completed_items=completed_items_count,
                    total_items=len(items)
                )
                await task_crud.update(db, db_obj=task, obj_in=task_update_data)

                # 4. 如果有待执行的任务项（非完成状态的任务项），加入调度队列
                if pending_items > 0:
                    # 3. 添加任务到调度队列（使用Redis队列）
                    try:
                        # 序列化当前的trace context
                        trace_context = serialize_trace_context()

                        # 将任务添加到Redis队列，包含trace context
                        await redis_queue.push(task_id, priority, trace_context)
                        logger.info(f"任务 {task_id} (优先级: {priority}) 已加入Redis队列，trace context已保存")
                    except Exception as e:
                        logger.error(f"将任务添加到Redis队列失败: {str(e)}")
                        # 回退到调度器的内存队列（兼容旧版本）
                        try:
                            await scheduler.add_task(task_id, priority)
                        except Exception as e2:
                            logger.error(f"将任务添加到调度器队列失败: {str(e2)}")

        except Exception as e:
            logger.error(f"处理任务项失败: {str(e)}")
            # 确保在异常情况下更新任务状态为失败
            try:
                async with get_async_db() as db:
                    task = await task_crud.get(db, id=task_id)
                    if task and task.status == "initializing":
                        await task_crud.update(db, db_obj=task, obj_in={
                            "status": "failed",
                            "error": f"任务项创建过程异常: {str(e)}",
                            "completed_at": datetime.now()
                        })
                        logger.info(f"任务 {task_id} 因异常标记为失败状态")
            except Exception as update_error:
                logger.error(f"更新任务失败状态时出错: {str(update_error)}")

    @staticmethod
    async def get_tasks(
            db: AsyncSession,
            *,
            filter_params: TaskFilter,
            page_params: PageParams
    ) -> PageResponse[TaskResponse]:
        """
        获取任务列表
        
        Args:
            db: 数据库会话
            filter_params: 过滤参数
            skip: 跳过记录数
            limit: 返回记录数上限
            
        Returns:
            PageResponse[TaskResponse]: 任务列表
        """
        # 获取总数（修复：使用正确的计数方法）
        total = await task_crud.count_filtered(db, filter_params=filter_params)

        # 计算skip和limit
        skip = (page_params.page - 1) * page_params.page_size
        limit = page_params.page_size

        # 获取任务列表
        tasks = await task_crud.get_filtered(
            db,
            filter_params=filter_params,
            skip=skip,
            limit=limit
        )

        # 为每个任务获取进度信息
        result = []
        for task in tasks:
            # 直接传入task对象
            task_info = await TaskManager.get_task_progress(db, task=task)
            result.append(task_info)

        return PageResponse[TaskResponse](
            page=page_params.page,
            page_size=page_params.page_size,
            total_count=total,
            total_page=(total + limit - 1) // limit,
            items=result
        )

    @staticmethod
    async def get_task(
            db: AsyncSession,
            task_id: int
    ) -> TaskResponse:
        """
        获取任务详情

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            TaskResponse: 任务响应对象

        Raises:
            HTTPException: 任务不存在时抛出
        """
        task = await task_crud.get(db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        # 传入任务对象获取进度
        return await task_manager.get_task_progress(db, task=task)

    @staticmethod
    async def get_task_model(
            db: AsyncSession,
            task_id: int
    ) -> Optional[Task]:
        """
        获取任务模型对象（内部使用）

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            Optional[Task]: 任务模型对象或None
        """
        return await task_crud.get(db, id=task_id)

    @staticmethod
    async def get_task_items(
            db: AsyncSession,
            *,
            task_id: int,
            page_params: PageParams
    ) -> PageResponse[TaskItemResponse]:
        """
        获取任务的所有任务项
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            skip: 跳过记录数
            limit: 返回记录数上限
            
        Returns:
            PageResponse[TaskItemResponse]: 任务项列表
        """
        # 获取总数
        total = await task_item_crud.count_by_task_id(db, task_id=task_id)

        # 计算skip和limit
        skip = (page_params.page - 1) * page_params.page_size
        limit = page_params.page_size

        items = await task_item_crud.get_by_task_id_paginated(
            db,
            task_id=task_id,
            skip=skip,
            limit=limit
        )

        # 转换为API响应格式
        result = []
        for item in items:
            result.append({
                "task_id": item.task_id,
                "task_item_id": item.id,
                "data_id": item.data_id,
                "file_type": item.file_type,
                "media_type": item.media_type,
                "file_hash": item.file_hash,
                "file_url": item.file_url,
                "status": item.status,
                "error": item.error,
                "plugin_code": item.plugin_code,
                "plugin_version": item.plugin_version,
                "result": item.result,
                "started_at": item.started_at,
                "completed_at": item.completed_at
            })

        return PageResponse[TaskItemResponse](
            page=page_params.page,
            page_size=page_params.page_size,
            total_count=total,
            total_page=(total + limit - 1) // limit,
            items=result
        )

    @staticmethod
    async def get_task_progress(
            db: AsyncSession,
            task: Task  # 直接接收Task对象而不是task_id
    ) -> TaskResponse:
        """
        获取任务实时进度详情

        Args:
            db: 数据库会话
            task: 任务对象

        Returns:
            Dict[str, Any]: 任务进度详情
        """
        # 获取已完成任务项数量（包括completed、failed和canceled状态）
        finished_items = await task_item_crud.count_by_task_id(db, task_id=task.id, status=["completed", "failed", "canceled"])

        # 构建进度信息
        return TaskResponse(
            task_id=task.id,
            progress=f"{finished_items}/{task.total_items}",
            status=task.status,
            priority=task.priority,
            created_at=task.created_at,
            updated_at=task.updated_at,
            started_at=task.started_at,
            completed_at=task.completed_at
        )

    @staticmethod
    async def update_task_status(
            db: AsyncSession,
            *,
            task_id: int,
            status: str,
            error: Optional[str] = None
    ) -> Task:
        """
        更新任务状态
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 新状态
            error: 错误信息
            
        Returns:
            Task: 更新后的任务对象
        """
        task = await task_crud.get(db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        update_data = {
            "status": status,
            "updated_at": datetime.now()
        }

        # 更新时间戳
        if status == "running" and not task.started_at:
            update_data["started_at"] = datetime.now()
        elif status in ["completed", "failed", "canceled"]:
            update_data["completed_at"] = datetime.now()

        updated_task = await task_crud.update(db, db_obj=task, obj_in=update_data)
        return updated_task

    @staticmethod
    async def update_task_priority(
            db: AsyncSession,
            *,
            task_id: int,
            priority: int
    ) -> TaskResponse:
        """
        更新任务优先级
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            priority: 新的优先级
            
        Returns:
            Task: 更新后的任务对象
        """
        task = await task_crud.get(db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        if task.status in ["completed", "failed", "canceled"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"任务已{task.status}，无法调整优先级"
            )

        updated_task = await task_crud.update(
            db,
            db_obj=task,
            obj_in={
                "priority": priority,
                "updated_at": datetime.now()
            }
        )

        # 更新调度队列中的优先级
        if updated_task.status in ["pending", "running"]:
            from app.core.task.scheduler import scheduler
            await scheduler.add_task(updated_task.id, priority)

        # 传入任务对象获取进度
        return await task_manager.get_task_progress(db, task=updated_task)


    @staticmethod
    async def cancel_task(
            db: AsyncSession,
            task_id: int
    ) -> TaskResponse:
        """
        取消任务

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            任务信息

        Raises:
            ValueError: 任务不存在或任务状态不允许取消
        """
        # 1. 获取任务信息
        task = await task_crud.get(db, id=task_id)
        if not task:
            raise ValueError("任务不存在")

        # 2. 检查任务状态是否允许取消
        if task.status in ["completed", "failed", "canceled"]:
            raise ValueError(f"任务状态为 {task.status}，不允许取消")
            
        # 3. 检查任务项是否已全部创建完成
        total_created_items = await task_item_crud.count_by_task_id(db, task_id=task_id)
        if total_created_items != task.total_items:
            raise ValueError(f"任务项尚未创建完成，已创建 {total_created_items}/{task.total_items}，不允许取消")

        # 4. 更新任务状态为已取消
        now = datetime.now()
        task.status = "canceled"
        task.completed_at = now
        task.updated_at = now
        await db.commit()

        # 5. 更新未完成的任务项状态为已取消
        await task_item_crud.update_status_by_task_id(
            db,
            task_id=task_id,
            old_status=["pending", "running"],
            new_status="canceled"
        )

        # 6. 从调度队列中移除任务
        from app.core.task.scheduler import scheduler
        await scheduler.remove_task(task_id)

        # 7. 返回更新后的任务信息
        return await task_manager.get_task_progress(db, task=task)

    @staticmethod
    async def get_pending_items(
            db: AsyncSession,
            task_id: int,
            limit: Optional[int] = None
    ) -> List[TaskItem]:
        """
        获取任务的待处理任务项
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            limit: 返回数量限制
            
        Returns:
            List[TaskItem]: 待处理的任务项列表
        """
        return await task_item_crud.get_by_status(
            db,
            task_id=task_id,
            status="pending",
            limit=limit
        )

    @staticmethod
    async def update_task_item(
            db: AsyncSession,
            *,
            id: int,
            update_data: Dict[str, Any]
    ) -> TaskItem:
        """
        更新任务项信息

        Args:
            db: 数据库会话
            id: 任务项ID
            update_data: 更新数据

        Returns:
            TaskItem: 更新后的任务项
        """
        item = await task_item_crud.get(db, id=id)
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务项 {id} 不存在"
            )
        return await task_item_crud.update(db, db_obj=item, obj_in=update_data)

    @staticmethod
    async def update_task_item_direct(
            db: AsyncSession,
            *,
            id: int,
            update_data: Dict[str, Any]
    ) -> bool:
        """
        直接更新任务项信息（不先查询，用于性能优化）

        Args:
            db: 数据库会话
            id: 任务项ID
            update_data: 更新数据

        Returns:
            bool: 是否更新成功
        """
        return await task_item_crud.update_direct(db, id=id, update_data=update_data)

    @staticmethod
    async def get_task_item(
            db: AsyncSession,
            *,
            id: int
    ) -> Optional[TaskItem]:
        """获取单个任务项"""
        return await task_item_crud.get(db, id=id)




# 创建全局任务管理器实例
task_manager = TaskManager()
