"""
插件执行器模块
负责协调插件执行的整体流程
"""
from typing import Dict, Any
import asyncio
import traceback

from app.log import plugin_logger as logger
from app.plugins.engines import get_engine_pool
from app.plugins.engines.compat import PluginMethodProxy
from app.plugins.loader import PluginLoader
from app.plugins.exceptions import (
    PluginExecutionException,
    EngineInitException,
    UnsupportedEngineException
)
from app.utils.trace_context_utils import traced_async_task, add_span_attributes, add_span_event, create_child_span


class PluginExecutor:
    """插件执行器，负责协调插件的执行流程"""
    
    @staticmethod
    @traced_async_task(
        "plugin_execution_{plugin_code}",
        attributes={
            "component": "plugin_executor",
            "operation": "execute_plugin"
        }
    )
    async def execute_plugin(
        plugin_info: Dict[str, Any],
        input_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        执行完整的插件处理流程
        
        Args:
            plugin_info: 插件信息字典（PluginInfo）
            input_data: 输入数据
            
        Returns:
            处理结果
            
        Raises:
            PluginExecutionException: 插件执行异常
        """
        try:
            # 获取插件代码和版本用于日志记录
            plugin_code = plugin_info["plugin_code"]
            plugin_version = plugin_info["plugin_version"]

            # 添加span属性（注意不记录敏感数据）
            add_span_attributes(
                plugin_code=plugin_code,
                plugin_version=plugin_version,
                plugin_engine=plugin_info.get("engine", "unknown"),
                input_file_type=input_data.get("file_type", "unknown"),
                input_media_type=input_data.get("media_type", "unknown"),
                task_id=input_data.get("task_id"),
                task_item_id=input_data.get("task_item_id")
            )
            add_span_event("plugin_execution_started")
            
            # 1. 加载插件
            add_span_event("plugin_loading_started")
            logger.debug(f"加载插件: {plugin_code} {plugin_version}")
            with create_child_span("plugin_loading", attributes={"plugin_code": plugin_code}):
                plugin = PluginLoader.load_plugin(plugin_info)
            add_span_event("plugin_loading_completed")

            # 2. 获取引擎池
            add_span_event("engine_acquisition_started")
            with create_child_span("engine_acquisition", attributes={"engine": plugin_info.get("engine", "unknown")}):
                engine_pool = await get_engine_pool()
            add_span_event("engine_acquisition_completed")
            
            # 3. 执行插件流程
            logger.info(f"开始执行插件: {plugin_code} {plugin_version} ,input_data: {input_data}")
            add_span_event("plugin_processing_started")

            # 使用上下文管理器自动处理引擎生命周期
            with create_child_span(
                "plugin_processing",
                attributes={
                    "plugin_code": plugin_code,
                    "plugin_version": plugin_version,
                    "engine": plugin_info.get("engine", "unknown")
                }
            ):
                async with engine_pool.get_engine_context(plugin_info) as engine:
                    # 创建插件方法代理，支持同步/异步方法
                    plugin_proxy = PluginMethodProxy(plugin)

                    # 执行统一处理方法
                    logger.debug(f"执行插件处理: {plugin_code} {plugin_version}")
                    try:
                        add_span_event("plugin_method_execution_started")
                        result = await plugin_proxy.process(input_data, engine)
                        add_span_event("plugin_method_execution_completed")

                        # 记录结果的基本信息（不记录敏感内容）
                        if isinstance(result, dict):
                            result_info = {
                                "has_result": "result" in result,
                                "has_progress_time": "progress_time" in result,
                                "result_keys": list(result.keys()) if result else []
                            }
                            add_span_attributes(**{f"result.{k}": str(v) for k, v in result_info.items()})

                    except Exception as e:
                        add_span_event("plugin_method_execution_failed", {"error": str(e)})
                        logger.exception(f"插件处理失败: {plugin_code} {plugin_version}")
                        raise PluginExecutionException(f"处理失败: {str(e)}")

                    logger.info(f"插件执行完成: {plugin_code} {plugin_version}")
                    add_span_event("plugin_processing_completed")
                    return result
                
        except (UnsupportedEngineException, EngineInitException) as e:
            add_span_event("plugin_execution_failed", {
                "error_type": "engine_error",
                "error_message": str(e)
            })
            logger.exception(f"引擎错误: {str(e)}")
            raise PluginExecutionException(f"引擎错误: {str(e)}")
        except PluginExecutionException as e:
            add_span_event("plugin_execution_failed", {
                "error_type": "plugin_execution_error",
                "error_message": str(e)
            })
            # 直接重新抛出PluginExecutionException类型的异常
            raise
        except Exception as e:
            add_span_event("plugin_execution_failed", {
                "error_type": "unexpected_error",
                "error_message": str(e)
            })
            # 捕获所有其他类型的异常，并包装为PluginExecutionException
            error_msg = f"插件执行过程中发生错误: {str(e)}"
            logger.exception(error_msg)
            raise PluginExecutionException(f"{error_msg}\n{traceback.format_exc()}")
