"""
ONNX Runtime推理引擎实现
"""
from typing import Dict, Any, List, Union
import time
import os

# 避免在导入时就加载onnxruntime，延迟导入
# 减小启动时资源占用
ort = None

from app.log import plugin_logger as logger
from app.plugins.engines.base import InferenceEngine
from app.plugins.exceptions import ModelLoadException, PredictionException, EngineInitException


class ONNXRuntimeEngine(InferenceEngine):
    """ONNX Runtime推理引擎"""
    
    def __init__(self):
        """初始化ONNX引擎"""
        self.session = None
        self.input_names = []
        self.output_names = []
        self.execution_providers = []
        self.model_path = None
        self.config = {}
        
    async def initialize(self, model_path: str, config: Dict[str, Any]) -> None:
        """
        初始化ONNX Runtime会话
        
        Args:
            model_path: ONNX模型文件路径
            config: 配置参数，包括:
                - execution_provider: 执行提供者，可以是字符串或列表
                  例如: 'CPUExecutionProvider' 或 
                  ['CUDAExecutionProvider', 'CPUExecutionProvider']
                - inter_op_num_threads: 线程间并行数
                - intra_op_num_threads: 线程内并行数
                - other_session_options: 其他会话选项
        
        Raises:
            ModelLoadException: 模型加载失败时抛出
        """
        global ort
        if ort is None:
            # 延迟导入onnxruntime，减少启动时资源占用
            import onnxruntime as ort
        
        try:
            self.model_path = model_path
            self.config = config or {}
            
            # 确认模型文件存在
            if not os.path.exists(model_path):
                raise ModelLoadException(f"模型文件不存在: {model_path}", "onnxruntime")
            
            # 获取执行提供者
            execution_provider = self.config.get('execution_provider', 'CPUExecutionProvider')
            
            # 支持字符串或列表形式的执行提供者配置
            if isinstance(execution_provider, str):
                self.execution_providers = [execution_provider]
            elif isinstance(execution_provider, list):
                self.execution_providers = execution_provider
            else:
                logger.warning(f"无效的执行提供者配置: {execution_provider}，使用默认CPU执行提供者")
                self.execution_providers = ['CPUExecutionProvider']
            
            # 获取可用的执行提供者
            available_providers = ort.get_available_providers()
            logger.info(f"ONNX Runtime可用的执行提供者: {available_providers}")
            logger.info(f"默认配置的 ONNX Runtime执行提供者: {self.execution_providers}")

            # 检查CUDAExecutionProvider是否可用，不可用则降级使用CPUExecutionProvider
            final_providers = []
            for provider in self.execution_providers:
                if provider in available_providers:
                    final_providers.append(provider)
                else:
                    logger.warning(f"执行提供者 {provider} 不可用，将被跳过")
            
            # 如果没有可用的执行提供者，使用CPUExecutionProvider
            if not final_providers:
                logger.warning(f"请求的执行提供者 {self.execution_providers} 均不可用，将使用CPUExecutionProvider")
                final_providers = ["CPUExecutionProvider"]
                
            self.execution_providers = final_providers
            
            # 创建会话选项
            session_options = ort.SessionOptions()
            
            # 设置线程数
            if 'inter_op_num_threads' in self.config:
                session_options.inter_op_num_threads = self.config['inter_op_num_threads']
            if 'intra_op_num_threads' in self.config:
                session_options.intra_op_num_threads = self.config['intra_op_num_threads']
            
            # 设置日志级别
            session_options.log_severity_level = 2  # 2 = warning
            
            # 其他会话选项
            other_options = self.config.get('other_session_options', {})
            for key, value in other_options.items():
                if hasattr(session_options, key):
                    setattr(session_options, key, value)
            
            logger.info(f"正在加载ONNX模型: {model_path}, 提供者优先级: {self.execution_providers}")
            start_time = time.time()
            
            # 创建推理会话
            self.session = ort.InferenceSession(
                model_path, 
                providers=self.execution_providers,
                sess_options=session_options
            )
            
            # 获取实际使用的执行提供者
            actual_providers = self.session.get_providers()
            logger.info(f"ONNX模型实际使用的执行提供者: {actual_providers}")
            
            # 获取输入输出名称
            self.input_names = [input.name for input in self.session.get_inputs()]
            self.output_names = [output.name for output in self.session.get_outputs()]
            
            load_time = time.time() - start_time
            logger.info(f"ONNX模型加载完成，耗时: {load_time:.2f}秒, 输入: {self.input_names}, 输出: {self.output_names}")
            
        except Exception as e:
            logger.exception(f"ONNX引擎初始化失败: {str(e)}")
            if "could not find the specified DLL" in str(e) or "failed to load" in str(e):
                raise ModelLoadException(f"ONNX依赖库加载失败: {str(e)}", "onnxruntime")
            raise ModelLoadException(f"ONNX模型加载失败: {str(e)}", "onnxruntime")
    
    async def get_client(self) -> Dict[str, Any]:
        """
        获取ONNX引擎客户端
        
        Returns:
            包含session和元数据的字典
            
        Raises:
            EngineInitException: 客户端获取失败时抛出
        """
        if not self.session:
            raise EngineInitException("ONNX会话尚未初始化", "onnxruntime")
            
        return {
            "session": self.session,
            "input_names": self.input_names,
            "output_names": self.output_names,
            "model_path": self.model_path,
            "execution_providers": self.execution_providers
        }
            
    async def destroy(self) -> None:
        """销毁ONNX会话资源"""
        logger.info(f"销毁ONNX引擎资源: {self.model_path}")
        self.session = None
        self.input_names = []
        self.output_names = []
        self.execution_providers = [] 