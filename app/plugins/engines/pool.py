"""
推理引擎池管理器
统一管理引擎实例的创建、复用和生命周期

引擎生命周期:
1. 初始化: 加载模型和资源
2. 获取客户端: 返回可用于推理的客户端对象
3. 销毁: 释放资源

引擎状态:
- CREATING: 正在创建中
- ACTIVE: 活跃状态，有引用计数
- IDLE: 闲置状态，可被复用
- ERROR: 错误状态
- RELEASING: 正在释放资源
"""
from typing import Dict, Any, Optional
import asyncio
import time
from contextlib import asynccontextmanager

from app.log import plugin_logger as logger
from app.plugins.engines.base import InferenceEngine
from app.plugins.engines.onnx import ONNXRuntimeEngine
from app.plugins.exceptions import (
    UnsupportedEngineException,
    EngineInitException,
    EngineResourceException
)
from app.plugins.engines.compat import DEFAULT_CONFIG, load_plugin_engine_config
from app.schemas.plugin import EngineType


class EnginePoolManager:
    """推理引擎池管理器 - 统一管理引擎实例的创建、复用和生命周期"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化引擎池管理器
        
        Args:
            config: 配置参数，如果为None则使用默认配置
        """
        self.config = config or DEFAULT_CONFIG
        self.instances = {}  # {key: {engine, last_used, status, ref_count, ...}}
        self.locks = {}      # {key: asyncio.Lock()}
        self.stats = {       # 统计信息
            "total_created": 0,
            "total_released": 0,
            "cache_hits": 0, 
            "cache_misses": 0,
            "errors": 0,
            "current_active": 0
        }
        self._cleanup_task = None
        self._stats_task = None  # 统计信息记录任务
        self._global_lock = asyncio.Lock()  # 用于控制全局资源限制
        
        logger.info(f"引擎池管理器初始化完成，配置: {self.config}")
        
        # 启动统计信息记录任务
        stats_interval = self.config.get("stats_interval", 300)  # 默认每5分钟记录一次
        if stats_interval > 0:
            self._stats_task = asyncio.create_task(self._log_stats_periodically(stats_interval))
        
    @asynccontextmanager
    async def get_engine_context(self, plugin_info: Dict[str, Any]):
        """
        上下文管理器模式获取引擎实例
        
        Args:
            plugin_info: 插件信息
            
        Yields:
            引擎实例或None
            
        示例:
            async with engine_pool.get_engine_context(plugin_info) as engine:
                # 传统方式使用
                result = await engine.run(input_data)
                
                # 或者使用客户端模式
                client = await engine.get_client()
                # 使用客户端进行自定义操作
        """
        plugin_code = plugin_info.get("plugin_code")
        plugin_version = plugin_info.get("plugin_version")
        engine = await self.get_engine(plugin_info)
        
        try:
            yield engine
        finally:
            if engine:
                await self.destroy_engine(plugin_code, plugin_version)
    
    async def get_engine(self, plugin_info: Dict[str, Any]) -> Optional[InferenceEngine]:
        """
        获取引擎实例，如实例不存在则创建
        支持IDLE状态实例的复用
        
        Args:
            plugin_info: 插件信息
            
        Returns:
            引擎实例或None(simple引擎)
        
        Raises:
            EngineResourceException: 资源限制异常
            UnsupportedEngineException: 不支持的引擎类型
            EngineInitException: 引擎初始化异常
        """
        plugin_code = plugin_info.get("plugin_code")
        plugin_version = plugin_info.get("plugin_version")
        engine_type = plugin_info.get("engine", "").lower()
        
        # simple引擎类型直接返回None
        if engine_type == "simple":
            return None
        
        # 生成实例键
        instance_key = f"{plugin_code}:{plugin_version}"
        
        # 检查实例是否存在且可用（ACTIVE或IDLE）
        if instance_key in self.instances and (
            self.instances[instance_key]["status"] == "ACTIVE" or 
            self.instances[instance_key]["status"] == "IDLE"
        ):
            # 如果是IDLE状态，更新为ACTIVE
            if self.instances[instance_key]["status"] == "IDLE":
                old_status = self.instances[instance_key]["status"]
                self.instances[instance_key]["status"] = "ACTIVE"
                self.instances[instance_key]["ref_count"] = 1
                logger.info(f"引擎状态变更: {instance_key}, {old_status} -> ACTIVE, 引用计数: 1, 引擎类型: {engine_type}")
            else:
                self.instances[instance_key]["ref_count"] += 1
                logger.info(f"引擎缓存命中: {instance_key}, 引用计数: {self.instances[instance_key]['ref_count']}, 引擎类型: {engine_type}")
            
            self._update_stats(instance_key, "hit")
            self.instances[instance_key]["last_used"] = time.time()
            return self.instances[instance_key]["engine"]
        
        self._update_stats(instance_key, "miss")
        if instance_key in self.instances:
            logger.info(f"引擎缓存未命中: {instance_key}, 当前状态: {self.instances[instance_key]['status']}, 引擎类型: {engine_type}")
        else:
            logger.info(f"引擎缓存未命中: {instance_key}, 原因: 实例不存在, 引擎类型: {engine_type}")
        
        # 检查资源限制
        max_instances = self.config.get("max_instances")
        if max_instances and len(self.instances) >= max_instances:
            # 如果有IDLE实例，可以尝试清理再创建
            await self._cleanup_idle_instances(force=True)
            if len(self.instances) >= max_instances:
                raise EngineResourceException(
                    f"达到最大引擎实例数限制: {max_instances}"
                )
        
        # 创建实例需要获取锁
        if instance_key not in self.locks:
            self.locks[instance_key] = asyncio.Lock()
            
        async with self.locks[instance_key]:
            # 双重检查
            if instance_key in self.instances and (
                self.instances[instance_key]["status"] == "ACTIVE" or 
                self.instances[instance_key]["status"] == "IDLE"
            ):
                # 如果是IDLE状态，更新为ACTIVE
                if self.instances[instance_key]["status"] == "IDLE":
                    old_status = self.instances[instance_key]["status"]
                    self.instances[instance_key]["status"] = "ACTIVE"
                    self.instances[instance_key]["ref_count"] = 1
                    logger.info(f"引擎状态变更: {instance_key}, {old_status} -> ACTIVE, 引用计数: 1, 引擎类型: {engine_type}")
                else:
                    self.instances[instance_key]["ref_count"] += 1
                    logger.info(f"引擎缓存命中: {instance_key}, 引用计数: {self.instances[instance_key]['ref_count']}, 引擎类型: {engine_type}")
                
                self._update_stats(instance_key, "hit")
                self.instances[instance_key]["last_used"] = time.time()
                return self.instances[instance_key]["engine"]
            
            # 如果实例存在但状态为ERROR，需要重新创建
            if instance_key in self.instances and self.instances[instance_key]["status"] == "ERROR":
                # 尝试释放错误实例
                try:
                    if self.instances[instance_key]["engine"]:
                        await self.instances[instance_key]["engine"].destroy()
                except Exception as e:
                    logger.warning(f"释放错误实例失败: {instance_key}, 错误: {e}")
            
            # 更新实例状态为CREATING
            self.instances[instance_key] = {
                "engine": None,
                "last_used": time.time(),
                "status": "CREATING",
                "ref_count": 1,
                "create_time": time.time(),
                "error": None,
                "usage_count": 0,
                "plugin_info": {
                    "code": plugin_code,
                    "version": plugin_version,
                    "engine_type": engine_type
                }
            }
            
            # 启动清理任务（如果尚未启动）
            if not self._cleanup_task or self._cleanup_task.done():
                cleanup_interval = self.config.get("cleanup_interval", 60)
                self._cleanup_task = asyncio.create_task(
                    self._cleanup_idle_instances(interval=cleanup_interval)
                )
            
            try:
                # 创建引擎
                engine = await self._create_engine(plugin_info)
                
                # 更新实例信息
                self.instances[instance_key].update({
                    "engine": engine,
                    "status": "ACTIVE",
                    "last_used": time.time()
                })
                
                self._update_stats(instance_key, "create")
                return engine
                
            except Exception as e:
                # 创建失败，更新为ERROR状态
                error_msg = str(e)
                self.instances[instance_key].update({
                    "status": "ERROR",
                    "error": error_msg,
                    "ref_count": 0
                })
                self._update_stats(instance_key, "error")
                
                logger.error(f"创建引擎失败: {instance_key}, 错误: {error_msg}")
                
                if isinstance(e, (UnsupportedEngineException, EngineInitException)):
                    raise
                raise EngineInitException(f"引擎初始化失败: {error_msg}")
    
    async def destroy_engine(self, plugin_code: str, plugin_version: str) -> None:
        """
        释放引擎实例（减少引用计数）
        
        Args:
            plugin_code: 插件编码
            plugin_version: 插件版本
        """
        instance_key = f"{plugin_code}:{plugin_version}"
        
        if instance_key in self.instances:
            # 更新使用时间
            self.instances[instance_key]["last_used"] = time.time()
            
            # 减少引用计数
            old_ref_count = self.instances[instance_key]["ref_count"]
            if old_ref_count > 0:
                self.instances[instance_key]["ref_count"] -= 1
            
            # 增加使用次数统计
            self.instances[instance_key]["usage_count"] += 1
            
            # 如果引用计数为0，标记为IDLE
            if self.instances[instance_key]["ref_count"] <= 0:
                old_status = self.instances[instance_key]["status"]
                self.instances[instance_key]["status"] = "IDLE"
                self.instances[instance_key]["ref_count"] = 0
                logger.info(f"引擎状态变更: {instance_key}, {old_status} -> IDLE, 引用计数: 0, 闲置超时: {self.config.get('idle_timeout', 300)}秒")
            else:
                logger.info(f"引擎引用计数减少: {instance_key}, {old_ref_count} -> {self.instances[instance_key]['ref_count']}")
        else:
            logger.warning(f"尝试释放不存在的引擎实例: {instance_key}")
    
    async def _create_engine(self, plugin_info: Dict[str, Any]) -> InferenceEngine:
        """
        创建引擎实例
        
        Args:
            plugin_info: 插件信息
            
        Returns:
            初始化的引擎实例
            
        Raises:
            UnsupportedEngineException: 不支持的引擎类型
            EngineInitException: 引擎初始化异常
        """
        engine_type = plugin_info.get("engine", "").lower()
        model_file_path = plugin_info.get("model_file_path", "")
        
        # 加载引擎配置
        engine_config = load_plugin_engine_config(plugin_info)
        
        # 创建对应类型的引擎（不区分大小写）
        if engine_type.lower() == EngineType.ONNXRuntime:
            engine = ONNXRuntimeEngine()
        else:
            raise UnsupportedEngineException(f"不支持的引擎类型: '{engine_type}'")
        
        # 初始化引擎
        try:
            await engine.initialize(model_file_path, engine_config)
            return engine
        except Exception as e:
            logger.error(f"引擎初始化错误: {str(e)}")
            raise EngineInitException(f"初始化{engine_type}引擎失败: {str(e)}")
    
    async def _cleanup_idle_instances(self, force=False, interval=60) -> None:
        """
        清理闲置实例
        
        Args:
            force: 是否强制清理（不管闲置时间）
            interval: 定期清理的时间间隔(秒)
        """
        if not force:
            # 定期清理模式
            while True:
                await asyncio.sleep(interval)
                await self._perform_cleanup()
                
                # 如果没有实例，退出清理任务
                if not self.instances:
                    logger.debug("引擎池为空，停止清理任务")
                    break
        else:
            # 强制清理模式（立即执行一次）
            await self._perform_cleanup(force)
    
    async def _perform_cleanup(self, force=False) -> None:
        """执行实际的清理操作"""
        current_time = time.time()
        keys_to_remove = []
        idle_timeout = self.config.get("idle_timeout", 300)
        
        for key, info in self.instances.items():
            # 只清理IDLE状态且超时的实例
            if info["status"] == "IDLE" and info["ref_count"] <= 0:
                if force or (current_time - info["last_used"] > idle_timeout):
                    keys_to_remove.append(key)
        
        # 释放资源
        for key in keys_to_remove:
            # 获取锁以安全删除
            if key in self.locks:
                try:
                    async with self.locks[key]:
                        if key in self.instances and self.instances[key]["status"] == "IDLE":
                            self.instances[key]["status"] = "RELEASING"
                            engine = self.instances[key]["engine"]
                            plugin_info = self.instances[key].get("plugin_info", {})
                            
                            logger.info(f"清理闲置引擎: {key}, 类型: {plugin_info.get('engine_type')}")
                            
                            try:
                                if engine:
                                    await engine.destroy()
                                self._update_stats(key, "release")
                            except Exception as e:
                                logger.error(f"释放引擎资源失败: {key}, 错误: {e}")
                            finally:
                                if key in self.instances:
                                    del self.instances[key]
                except Exception as e:
                    logger.error(f"清理引擎过程中发生错误: {key}, 错误: {e}")
    
    def _update_stats(self, key: str, action: str) -> None:
        """
        更新统计信息
        
        Args:
            key: 实例键
            action: 操作类型 (hit/miss/create/error/release)
        """
        if action == "hit":
            self.stats["cache_hits"] += 1
        elif action == "miss":
            self.stats["cache_misses"] += 1
        elif action == "create":
            self.stats["total_created"] += 1
            self.stats["current_active"] += 1
        elif action == "release":
            self.stats["total_released"] += 1
            self.stats["current_active"] -= 1
        elif action == "error":
            self.stats["errors"] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取池统计信息
        
        Returns:
            统计信息字典
        """
        # 基本统计
        stats = {**self.stats}
        
        # 计算缓存命中率
        total_requests = stats["cache_hits"] + stats["cache_misses"]
        if total_requests > 0:
            stats["cache_hit_ratio"] = stats["cache_hits"] / total_requests
        else:
            stats["cache_hit_ratio"] = 0
            
        # 实例详情
        instance_stats = {}
        for key, info in self.instances.items():
            instance_stats[key] = {
                "status": info["status"],
                "ref_count": info["ref_count"],
                "usage_count": info.get("usage_count", 0),
                "idle_time": time.time() - info["last_used"] if info["status"] == "IDLE" else 0,
                "alive_time": time.time() - info["create_time"],
                "engine_type": info.get("plugin_info", {}).get("engine_type"),
                "error": info.get("error")
            }
        
        stats["instances"] = instance_stats
        stats["total_instances"] = len(self.instances)
        
        return stats
    
    async def shutdown(self) -> None:
        """关闭所有引擎实例（应用关闭时调用）"""
        logger.info("开始关闭引擎池...")
        
        # 取消清理任务
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 取消统计任务
        if self._stats_task and not self._stats_task.done():
            self._stats_task.cancel()
            try:
                await self._stats_task
            except asyncio.CancelledError:
                pass
        
        # 释放所有实例
        for key, info in list(self.instances.items()):
            try:
                if info["engine"]:
                    logger.info(f"关闭引擎: {key}")
                    await info["engine"].destroy()
            except Exception as e:
                logger.error(f"关闭引擎失败: {key}, 错误: {e}")
        
        self.instances.clear()
        self.locks.clear()
        logger.info("引擎池成功关闭")
    
    async def _log_stats_periodically(self, interval: int) -> None:
        """
        定期记录引擎池统计信息
        
        Args:
            interval: 记录间隔(秒)
        """
        try:
            while True:
                await asyncio.sleep(interval)
                stats = self.get_stats()
                
                # 记录基本统计信息
                logger.info(
                    f"引擎池统计 - 总实例数: {stats['total_instances']}, "
                    f"活跃实例数: {stats['current_active']}, "
                    f"缓存命中率: {stats['cache_hit_ratio']:.2f}, "
                    f"命中次数: {stats['cache_hits']}, "
                    f"未命中次数: {stats['cache_misses']}, "
                    f"创建次数: {stats['total_created']}, "
                    f"释放次数: {stats['total_released']}, "
                    f"错误次数: {stats['errors']}"
                )
                
                # 记录每个实例的详细信息
                for key, instance_info in stats.get("instances", {}).items():
                    logger.debug(
                        f"引擎实例 {key} - "
                        f"状态: {instance_info['status']}, "
                        f"引用计数: {instance_info['ref_count']}, "
                        f"使用次数: {instance_info['usage_count']}, "
                        f"闲置时间: {instance_info['idle_time']:.1f}秒, "
                        f"存活时间: {instance_info['alive_time']:.1f}秒, "
                        f"引擎类型: {instance_info['engine_type']}"
                    )
        except asyncio.CancelledError:
            logger.debug("引擎池统计任务已取消")
        except Exception as e:
            logger.error(f"引擎池统计任务异常: {e}") 